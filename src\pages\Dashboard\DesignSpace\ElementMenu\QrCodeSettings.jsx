import { useF<PERSON>, Controller } from 'react-hook-form';


import { getFormErrorMessage } from '@utils/helper'
import { useUploadMutation } from '@quires';

import { classNames } from 'primereact/utils';
import { InputText } from 'primereact/inputtext';
import { useMemo, useState } from 'react';
import { isEmpty } from 'lodash';
import LoadOnScroll from './LoadOnScroll';
import QRCode from 'react-qr-code';

function QrCodeSettings() {
  // const { addElement } = useDesignSpace();
  const { formState: { errors }, handleSubmit, control, watch, reset } = useForm({
    defaultValues: { color: '#000000' }
  });
  const [refetch, setRefetch] = useState(true);

  const uploadImage = useUploadMutation()

  const onSubmit = async (data) => {
    await uploadImage.mutateAsync(
      {
        user_id: localStorage.getItem("user_id"),
        file_type: "qr",
        ...data,
        qr_link: data.data,
      },
      {
        onSuccess: () => {
          setRefetch(true)
          reset()
        }
      }
    )
  }

  const isDisabled = useMemo(() => isEmpty(watch("data")), [watch("data")])
  const qrValue = watch('data') || '';
  const qrColor = watch('color') || '#000000';
  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col">
        <div className="mb-2 w-full">
          <div className="field">
            <span className="p-float-label mt-2">
              <Controller name="data" control={control}
                rules={{ required: "This field is required!" }}
                render={({ field, fieldState }) => (
                  <InputText
                    id={field.name}
                    {...field}
                    ref={field.ref}
                    placeholder="URL"
                    className={`w-full text-[#696F79] p-3 ${classNames({ 'p-invalid': fieldState.invalid })}`} />
                )} />
            </span>
            {getFormErrorMessage('data', errors)}
          </div>
        </div>
        {/* QR Color Picker */}
        <div className="mb-2 w-full">
          <div className="field">
            <label className="block text-sm font-medium mb-2">QR Code Color</label>
            <Controller
              name="color"
              control={control}
              defaultValue="#000000"
              render={({ field }) => (
                <div className="flex flex-wrap gap-2 items-center">
                  {/* Color swatches */}
                  {[
                    '#000000', '#ffffff', '#FF0000', '#FF8000', '#FFFF00', '#80FF00', '#00FF00', '#00FF80', '#00FFFF', '#0080FF', '#0000FF', '#8000FF', '#FF00FF', '#FF0080',
                    '#FF6B6B', '#FF8E53', '#FFA726', '#FFB74D', '#FFCC02', '#FFD54F', '#FFE082', '#FFECB3', '#FFF3E0', '#FFAB91', '#FF8A65', '#FF7043',
                    '#4FC3F7', '#29B6F6', '#03A9F4', '#039BE5', '#0288D1', '#0277BD', '#01579B', '#E1F5FE', '#B3E5FC', '#81D4FA', '#8BC34A', '#CDDC39', '#FFEB3B', '#FF9800', '#FF5722', '#795548', '#9E9E9E', '#607D8B', '#3F51B5', '#2196F3', '#00BCD4', '#009688', '#FF1744', '#F50057', '#D500F9', '#651FFF', '#3D5AFE', '#2979FF', '#00B0FF', '#00E5FF', '#1DE9B6', '#00E676', '#76FF03', '#C6FF00'
                  ].map((color) => (
                    <button
                      type="button"
                      key={color}
                      className={`w-7 h-7 rounded-full border-2 transition-all ${field.value === color ? 'border-blue-600 scale-110' : 'border-gray-300'}`}
                      style={{ backgroundColor: color }}
                      onClick={() => field.onChange(color)}
                      aria-label={`Select color ${color}`}
                    />
                  ))}
                  {/* Custom color input */}
                  <input
                    type="color"
                    value={field.value}
                    onChange={e => field.onChange(e.target.value)}
                    className="w-8 h-8 rounded border ml-2 cursor-pointer"
                    aria-label="Custom color"
                  />
                  <input
                    type="text"
                    value={field.value}
                    onChange={e => field.onChange(e.target.value)}
                    className="w-20 ml-2 px-2 py-1 border rounded text-xs font-mono"
                    placeholder="#RRGGBB"
                  />
                </div>
              )}
            />
          </div>
        </div>
        {/* QR Code Preview */}
        <div className="flex flex-col items-center my-4">
          <label className="block text-xs text-gray-500 mb-1">Preview</label>
          <div className="bg-white p-3 rounded-lg shadow border" style={{ minWidth: 120, minHeight: 120 }}>
            <QRCode value={qrValue} size={120} fgColor={qrColor} bgColor="#fff" />
          </div>
        </div>
        <button
          className={`w-6/12 me-1 my-2 ${isDisabled ? "gray-btn" : "main-btn"} ms-auto`}
          disabled={isDisabled}
          type='submit'
        >
          Add Qr
        </button>
      </form>

      <div className="flex flex-col justify-start m-2">
        <h4 className="font-bold mt-4 mb-2 text-[#676666] flex items-center gap-2">
          <svg width="22" height="22" viewBox="0 0 24 24" fill="none"><rect x="2" y="2" width="7" height="7" rx="2" fill="#676666"/><rect x="2" y="15" width="7" height="7" rx="2" fill="#676666"/><rect x="15" y="2" width="7" height="7" rx="2" fill="#676666"/><rect x="15" y="15" width="7" height="7" rx="2" fill="#676666"/><rect x="11" y="11" width="2" height="2" rx="1" fill="#676666"/><rect x="11" y="15" width="2" height="2" rx="1" fill="#676666"/><rect x="15" y="11" width="2" height="2" rx="1" fill="#676666"/></svg>
          QR Code Gallery
        </h4>
        <LoadOnScroll fileType="qr" refetch={refetch} setRefetch={setRefetch} />
      </div>
    </>
  )
}

export default QrCodeSettings