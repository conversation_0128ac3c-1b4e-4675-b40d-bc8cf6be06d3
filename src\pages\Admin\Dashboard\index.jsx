import React, { useState, useEffect } from 'react'
import { useQuery } from 'react-query'
import axiosInstance from "../../../config/Axios";
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, PointElement, LineElement, Title, Tooltip, Legend } from 'chart.js';
import { Line } from 'react-chartjs-2';

// Icons
import {
  FiCreditCard,
  FiPackage,
  FiUsers,
  FiBarChart2,
  FiTrendingUp,
  FiCheckCircle,
  FiInfo,
  FiDatabase,
  FiShield,
  FiUserPlus
} from 'react-icons/fi';

// Register ChartJS components
ChartJS.register(CategoryScale, LinearScale, BarElement, PointElement, LineElement, Title, Tooltip, Legend);

// Fetch total cards count for admin
const fetchTotalCardsCount = async () => {
  try {
    const token = localStorage.getItem('token')
    const userId = localStorage.getItem('user_id')

    if (!token || !userId) {
      console.error("Token or user_id not found in localStorage")
      return 0
    }

    const response = await axiosInstance.get(`all_cards_admin`, {
      headers: {
        Authorization: `Bearer ${token}`,
      }
    })

    return response.data.data?.total || 0
  } catch (error) {
    console.error('Error fetching total cards count:', error)
    return 0
  }
}

// Fetch total companies count
const fetchTotalCompaniesCount = async () => {
  try {
    const token = localStorage.getItem('token')

    if (!token) {
      console.error("Token not found in localStorage")
      return 0
    }

    // Using the same API endpoint as the companies page
    const response = await axiosInstance.get("/companies", {
      headers: {
        Authorization: `Bearer ${token}`,
      }
    })

    // Return the total count from the array length (same as companies page)
    console.log('Total companies count:', response.data?.length || 0)
    return response.data?.length || 0
  } catch (error) {
    console.error('Error fetching companies count:', error)
    return 0
  }
}

// Fetch all packages
const fetchAllPackages = async () => {
  try {
    const token = localStorage.getItem('token');
    const userId = localStorage.getItem('user_id');

    if (!token || !userId) {
      console.error("Token or user_id not found in localStorage");
      return [];
    }

    const backendUrl = import.meta.env.VITE_BACKEND_URL;
    const apiUrl = `${backendUrl}/packages/show-all-packages`;

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error(`Error fetching packages: ${response.status}`);
      const errorText = await response.text();
      console.error('Error Response Body:', errorText);
      return [];
    }

    const contentType = response.headers.get('Content-Type');
    if (contentType && contentType.includes('application/json')) {
      const data = await response.json();
      console.log('Packages Data:', data);
      return data || [];
    } else {
      const responseText = await response.text();
      console.warn('Received non-JSON response:', responseText);
      return [];
    }

  } catch (error) {
    console.error('Error fetching all packages:', error);
    return [];
  }
};

// Fetch sold packages data for chart
const fetchSoldPackagesData = async () => {
  try {
    const token = localStorage.getItem('token');

    if (!token) {
      console.error("Token not found in localStorage");
      return [];
    }

    const response = await axiosInstance.get('packages/show-sold-packages', {
      headers: {
        Authorization: `Bearer ${token}`,
      }
    });

    console.log('Sold Packages Data:', response.data);
    return response.data || [];
  } catch (error) {
    console.error('Error fetching sold packages:', error);
    return [];
  }
};

// Fetch latest sold packages for display
const fetchLatestSoldPackages = async () => {
  try {
    const token = localStorage.getItem('token');

    if (!token) {
      console.error("Token not found in localStorage");
      return [];
    }

    const response = await axiosInstance.get('packages/show-sold-packages', {
      headers: {
        Authorization: `Bearer ${token}`,
      }
    });

    // Get the packages from the response
    const packages = response.data || [];

    // Sort by purchased_at date if available, otherwise just take the first few
    const sortedPackages = packages.sort((a, b) => {
      if (a.purchased_at && b.purchased_at) {
        return new Date(b.purchased_at) - new Date(a.purchased_at);
      }
      return 0;
    });

    // Return the latest 3 packages
    return sortedPackages.slice(0, 3);
  } catch (error) {
    console.error('Error fetching latest sold packages:', error);
    return [];
  }
};

// Fetch active packages count from new API
const fetchActivePackagesCount = async () => {
  try {
    const token = localStorage.getItem('token');
    const backendUrl = import.meta.env.VITE_BACKEND_URL;
    const apiUrl = `${backendUrl}/packages/count-active`;
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });
    if (!response.ok) {
      console.error(`Error fetching active packages count: ${response.status}`);
      return 0;
    }
    const data = await response.json();
    return data.active_packages_count || 0;
  } catch (error) {
    console.error('Error fetching active packages count:', error);
    return 0;
  }
};

// Fetch managers count from new API
const fetchManagersCount = async () => {
  try {
    const token = localStorage.getItem('token');
    const backendUrl = import.meta.env.VITE_BACKEND_URL;
    const apiUrl = `${backendUrl}/packages/count-managers`;
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });
    if (!response.ok) {
      console.error(`Error fetching managers count: ${response.status}`);
      return 0;
    }
    const data = await response.json();
    return data.managers_count || 0;
  } catch (error) {
    console.error('Error fetching managers count:', error);
    return 0;
  }
};


export default function AdminDashboard() {
  const { data: totalCardsCount, isLoading: cardsLoading, isError: cardsError } = useQuery('totalCardsCount', fetchTotalCardsCount)
  const { data: totalCompaniesCount, isLoading: companiesLoading, isError: companiesError } = useQuery('totalCompaniesCountFixed', fetchTotalCompaniesCount)
  const { data: allPackages, isLoading: packagesLoading, isError: packagesError } = useQuery('allPackages', fetchAllPackages)
  const { data: soldPackages, isLoading: soldPackagesLoading, isError: soldPackagesError } = useQuery('soldPackages', fetchSoldPackagesData)
  const { data: latestSoldPackages, isLoading: latestPackagesLoading, isError: latestPackagesError } = useQuery('latestSoldPackages', fetchLatestSoldPackages)
  const { data: activePackagesCount, isLoading: activePackagesLoading, isError: activePackagesError } = useQuery('activePackagesCount', fetchActivePackagesCount);
  const { data: managersCount, isLoading: managersLoading, isError: managersError } = useQuery('managersCount', fetchManagersCount);
  const [greeting, setGreeting] = useState('');
  const [currentTime, setCurrentTime] = useState('');
  const [chartTimeRange, setChartTimeRange] = useState('last6Months');
  const [chartView, setChartView] = useState('packages'); // 'packages' or 'revenue'

  // Process sold packages data for chart
  const processChartData = () => {
    if (!soldPackages || !Array.isArray(soldPackages)) return null;

    // Get current date and calculate date ranges based on selected time range
    const currentDate = new Date();
    let startDate;
    let dateFormat;

    switch (chartTimeRange) {
      case 'last7Days':
        startDate = new Date(currentDate);
        startDate.setDate(currentDate.getDate() - 7);
        dateFormat = { day: '2-digit', month: 'short' };
        break;
      case 'lastMonth':
        startDate = new Date(currentDate);
        startDate.setMonth(currentDate.getMonth() - 1);
        dateFormat = { day: '2-digit', month: 'short' };
        break;
      case 'last6Months':
        startDate = new Date(currentDate);
        startDate.setMonth(currentDate.getMonth() - 6);
        dateFormat = { month: 'short' };
        break;
      case 'lastYear':
        startDate = new Date(currentDate);
        startDate.setFullYear(currentDate.getFullYear() - 1);
        dateFormat = { month: 'short' };
        break;
      default:
        startDate = new Date(currentDate);
        startDate.setMonth(currentDate.getMonth() - 6);
        dateFormat = { month: 'short' };
    }

    // Filter packages by date range
    const filteredPackages = soldPackages.filter(pkg => {
      if (!pkg.purchased_at) return false;
      const purchaseDate = new Date(pkg.purchased_at);
      return purchaseDate >= startDate && purchaseDate <= currentDate;
    });

    // Group packages by month/date
    const groupedData = {};

    // Create labels based on time range
    const labels = [];

    if (chartTimeRange === 'last7Days') {
      // For last 7 days, create daily labels
      for (let i = 6; i >= 0; i--) {
        const date = new Date(currentDate);
        date.setDate(currentDate.getDate() - i);
        const label = date.toLocaleDateString(undefined, dateFormat);
        labels.push(label);
        groupedData[label] = { count: 0, revenue: 0 };
      }
    } else if (chartTimeRange === 'lastMonth') {
      // For last month, create weekly labels
      for (let i = 4; i >= 0; i--) {
        const date = new Date(currentDate);
        date.setDate(currentDate.getDate() - (i * 7));
        const label = date.toLocaleDateString(undefined, dateFormat);
        labels.push(label);
        groupedData[label] = { count: 0, revenue: 0 };
      }
    } else {
      // For last 6 months or last year, create monthly labels
      const monthCount = chartTimeRange === 'last6Months' ? 6 : 12;
      for (let i = monthCount - 1; i >= 0; i--) {
        const date = new Date(currentDate);
        date.setMonth(currentDate.getMonth() - i);
        const label = date.toLocaleDateString(undefined, dateFormat);
        labels.push(label);
        groupedData[label] = { count: 0, revenue: 0 };
      }
    }

    // Populate data
    filteredPackages.forEach(pkg => {
      const purchaseDate = new Date(pkg.purchased_at);
      let label;

      if (chartTimeRange === 'last7Days') {
        label = purchaseDate.toLocaleDateString(undefined, dateFormat);
      } else if (chartTimeRange === 'lastMonth') {
        // Group by week
        const weekNumber = Math.floor((currentDate - purchaseDate) / (7 * 24 * 60 * 60 * 1000));
        if (weekNumber < 5) {
          const weekStartDate = new Date(currentDate);
          weekStartDate.setDate(currentDate.getDate() - (weekNumber * 7));
          label = weekStartDate.toLocaleDateString(undefined, dateFormat);
        }
      } else {
        // Group by month
        label = purchaseDate.toLocaleDateString(undefined, dateFormat);
      }

      if (label && groupedData[label]) {
        groupedData[label].count += 1;
        groupedData[label].revenue += parseFloat(pkg.total_price || 0);
      }
    });

    // Extract data for chart
    const packageCounts = labels.map(label => groupedData[label]?.count || 0);
    const revenueData = labels.map(label => groupedData[label]?.revenue || 0);

    // Create datasets based on the selected view
    const datasets = [];

    if (chartView === 'packages' || chartView === 'both') {
      datasets.push({
        label: 'Packages Sold',
        data: packageCounts,
        borderColor: 'rgba(0, 195, 172, 1)',
        backgroundColor: 'rgba(0, 195, 172, 0.2)',
        borderWidth: 2,
        fill: true,
        tension: 0.4,
        pointRadius: 4,
        pointBackgroundColor: 'rgba(0, 195, 172, 1)',
        pointBorderColor: '#fff',
        pointBorderWidth: 2
      });
    }

    if (chartView === 'revenue' || chartView === 'both') {
      datasets.push({
        label: 'Revenue (USD)',
        data: revenueData,
        borderColor: 'rgba(54, 162, 235, 1)',
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
        borderWidth: 2,
        fill: true,
        tension: 0.4,
        pointRadius: 4,
        pointBackgroundColor: 'rgba(54, 162, 235, 1)',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        yAxisID: chartView === 'both' ? 'y1' : 'y'
      });
    }

    return {
      labels,
      datasets
    };
  };

  // Chart options
  const getChartOptions = () => {
    const options = {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: chartView === 'revenue' ? 'Revenue (USD)' : 'Packages Sold'
          }
        }
      },
      plugins: {
        legend: {
          position: 'top',
        },
        title: {
          display: true,
          text: 'Packages Sales Overview'
        }
      },
      elements: {
        line: {
          tension: 0.4 // Smoother curves
        }
      },
      interaction: {
        mode: 'index',
        intersect: false
      }
    };

    // Add second y-axis only if showing both datasets
    if (chartView === 'both') {
      options.scales.y1 = {
        beginAtZero: true,
        position: 'right',
        title: {
          display: true,
          text: 'Revenue (USD)'
        },
        grid: {
          drawOnChartArea: false
        }
      };
    }

    return options;
  };

  // Get current chart options based on view
  const chartOptions = getChartOptions();

  // Calculate time-based greeting
  useEffect(() => {
    const updateGreeting = () => {
      const hour = new Date().getHours();
      let greetingText = '';

      if (hour < 12) greetingText = 'Good Morning';
      else if (hour < 18) greetingText = 'Good Afternoon';
      else greetingText = 'Good Evening';

      setGreeting(greetingText);
    };

    const updateTime = () => {
      const now = new Date();
      const options = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      };
      setCurrentTime(now.toLocaleDateString(undefined, options));
    };

    updateGreeting();
    updateTime();

    const interval = setInterval(updateTime, 60000); // Update time every minute

    return () => clearInterval(interval);
  }, []);

  // Process chart data and update when view or time range changes
  const [chartData, setChartData] = useState(null);

  useEffect(() => {
    setChartData(processChartData());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [chartView, chartTimeRange, soldPackages]);

  if (cardsLoading || companiesLoading || packagesLoading || soldPackagesLoading || latestPackagesLoading || activePackagesLoading || managersLoading) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="animate-pulse flex flex-col items-center">
          <div className="w-16 h-16 border-4 border-t-transparent border-[#00c3ac] rounded-full animate-spin"></div>
          <p className="mt-4 text-gray-600">Loading admin dashboard data...</p>
        </div>
      </div>
    );
  }

  if (cardsError || companiesError || packagesError || soldPackagesError || latestPackagesError || activePackagesError || managersError) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">Error!</strong>
          <span className="block sm:inline"> Unable to load admin dashboard data. Please try again later.</span>
        </div>
      </div>
    );
  }

  return (
    <section className='w-full flex flex-col rounded-[6px] min-h-full'>
      {/* Welcome Section */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full bg-gradient-to-r from-[#3a0ca3] to-[#4361ee] p-6 rounded-[10px] shadow-lg mb-6 text-white"
      >
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">{greeting}, {localStorage.getItem("user_name") || "Admin"}</h1>
            <p className="text-white/80 mt-1">{currentTime}</p>
          </div>

          {/* Buttons for later use */}

          {/* <div className="hidden md:flex space-x-2">
            <Link to="/admin/settings" className="bg-white/20 hover:bg-white/30 p-2 rounded-full transition-all">
              <FiSettings size={20} />
            </Link>
            <Link to="/admin/billing" className="bg-white/20 hover:bg-white/30 p-2 rounded-full transition-all">
              <FiActivity size={20} />
            </Link>
          </div> */}
        </div>
      </motion.div>

      {/* Stats Cards */}
      <div className="w-full grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          className="bg-white p-5 rounded-[10px] shadow-md border border-gray-200 border-l-4 border-l-[#4361ee] hover:shadow-lg transition-all"
          style={{ backgroundColor: '#fff' }}
        >
          <div className="flex items-start justify-between">
            <div>
              <p className="text-gray-500 text-sm font-medium">Total Cards</p>
              <h3 className="text-2xl font-bold mt-2">{totalCardsCount}</h3>
            </div>
            <div className="bg-[#4361ee]/10 p-3 rounded-full">
              <FiCreditCard size={20} className="text-[#4361ee]" />
            </div>
          </div>
          <div className="mt-4 text-xs flex items-center text-blue-600">
            <FiInfo className="mr-1" />
            <span>System-wide card count</span>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          className="bg-white p-5 rounded-[10px] shadow-md border border-gray-200 border-l-4 border-l-green-500 hover:shadow-lg transition-all"
          style={{ backgroundColor: '#fff' }}
        >
          <div className="flex items-start justify-between">
            <div>
              <p className="text-gray-500 text-sm font-medium">Active Packages</p>
              <h3 className="text-2xl font-bold mt-2">{activePackagesCount}</h3>
            </div>
            <div className="bg-green-500/10 p-3 rounded-full">
              <FiPackage size={20} className="text-green-500" />
            </div>
          </div>
          <div className="mt-4 text-xs flex items-center text-green-600">
            <FiCheckCircle className="mr-1" />
            <span>Active packages across all companies</span>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, delay: 0.3 }}
          className="bg-white p-5 rounded-[10px] shadow-md border border-gray-200 border-l-4 border-l-purple-500 hover:shadow-lg transition-all"
          style={{ backgroundColor: '#fff' }}
        >
          <div className="flex items-start justify-between">
            <div>
              <p className="text-gray-500 text-sm font-medium">Total Managers</p>
              <h3 className="text-2xl font-bold mt-2">{managersCount}</h3>
            </div>
            <div className="bg-purple-500/10 p-3 rounded-full">
              <FiUsers size={20} className="text-purple-500" />
            </div>
          </div>
          <div className="mt-4 text-xs flex items-center text-purple-600">
            <FiTrendingUp className="mr-1" />
            <span>Members across all companies</span>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, delay: 0.4 }}
          className="bg-white p-5 rounded-[10px] shadow-md border border-gray-200 border-l-4 border-l-amber-500 hover:shadow-lg transition-all"
          style={{ backgroundColor: '#fff' }}
        >
          <div className="flex items-start justify-between">
            <div>
              <p className="text-gray-500 text-sm font-medium">Companies</p>
              <h3 className="text-2xl font-bold mt-2">{totalCompaniesCount}</h3>
            </div>
            <div className="bg-amber-500/10 p-3 rounded-full">
              <FiDatabase size={20} className="text-amber-500" />
            </div>
          </div>
          <div className="mt-4 text-xs flex items-center text-amber-600">
            <FiCheckCircle className="mr-1" />
            <span>Total registered companies</span>
          </div>
        </motion.div>
      </div>

      {/* Main Content Area --Same Between Both-- */}
      <div className="w-full grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Chart Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="md:col-span-2 bg-white p-5 border border-gray-200 rounded-[10px] shadow-md"
          style={{ backgroundColor: '#fff' }}
        >
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-bold text-gray-700">Packages Sales Overview</h2>
            <div className="flex items-center space-x-4">
              {/* Chart View Toggle */}
              <div className="flex items-center space-x-2 bg-gray-100 p-1 rounded-lg">
                <button
                  className={`px-3 py-1 text-xs font-medium rounded-md transition-all ${
                    chartView === 'packages'
                      ? 'bg-[#00c3ac] text-white shadow-sm'
                      : 'text-gray-600 hover:bg-gray-200'
                  }`}
                  onClick={() => setChartView('packages')}
                >
                  Packages
                </button>
                <button
                  className={`px-3 py-1 text-xs font-medium rounded-md transition-all ${
                    chartView === 'both'
                      ? 'bg-[#00c3ac] text-white shadow-sm'
                      : 'text-gray-600 hover:bg-gray-200'
                  }`}
                  onClick={() => setChartView('both')}
                >
                  Both
                </button>
                <button
                  className={`px-3 py-1 text-xs font-medium rounded-md transition-all ${
                    chartView === 'revenue'
                      ? 'bg-[#00c3ac] text-white shadow-sm'
                      : 'text-gray-600 hover:bg-gray-200'
                  }`}
                  onClick={() => setChartView('revenue')}
                >
                  Revenue (USD)
                </button>
              </div>

              {/* Time Range Selector */}
              <select
                className="text-sm border rounded p-1"
                value={chartTimeRange}
                onChange={(e) => setChartTimeRange(e.target.value)}
              >
                <option value="last7Days">Last 7 Days</option>
                <option value="lastMonth">Last Month</option>
                <option value="last6Months">Last 6 Months</option>
                <option value="lastYear">Last Year</option>
              </select>
            </div>
          </div>
          <div className="h-64 flex items-center justify-center">
            {chartData ? (
              <Line
                data={chartData}
                options={chartOptions}
                className="w-full h-full"
              />
            ) : (
              <div className="w-full h-full bg-gray-50 rounded-lg flex items-center justify-center">
                <div className="text-center text-gray-500">
                  <FiBarChart2 size={40} className="mx-auto mb-2 text-gray-400" />
                  <p>No package sales data available</p>
                </div>
              </div>
            )}
          </div>
        </motion.div>

        {/* Latest Packages Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="bg-white p-5 border border-gray-200 rounded-[10px] shadow-md"
          style={{ backgroundColor: '#fff' }}
        >
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-bold text-gray-700">Latest Packages</h2>
            <Link to="/admin/sold_P" className="text-[#4361ee] text-sm hover:underline">
              View All Packages
            </Link>
          </div>

          {latestSoldPackages && latestSoldPackages.length > 0 ? (
            <div className="space-y-4">
              {latestSoldPackages.map((pkg) => (
                <div key={pkg.id} className="flex items-start space-x-3 pb-3 border-b border-gray-100">
                  <div className="p-2 rounded-full bg-green-500/10 text-green-500">
                    <FiPackage />
                  </div>
                  <div className="flex-1">
                    <div className="flex justify-between">
                      <p className="text-sm font-medium">{pkg.name || 'Unnamed Package'}</p>
                      <p className="text-xs text-gray-500">${pkg.total_price || '0'}</p>
                    </div>
                    <p className="text-xs text-gray-500">
                      Limit: {pkg.card_limit || '0'} cards
                    </p>
                    <p className="text-xs text-gray-500 font-bold">
                      {pkg.purchased_by_manager_name || 'Unknown user'}
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      Purchased {pkg.purchased_at
                        ? new Date(pkg.purchased_at).toLocaleDateString(undefined, {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric'
                          })
                        : 'Unknown date'}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-6 text-gray-500">
              <FiPackage size={32} className="mb-2 text-gray-400" />
              <p>No packages purchased yet</p>
            </div>
          )}
        </motion.div>
      </div>

      {/* Admin Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
        className="w-full bg-gray-300 p-5 border border-gray-200 rounded-[10px] shadow-md mt-6"
        style={{ backgroundColor: '#d1d5db' }}
      >
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-bold text-gray-700">Admin Actions</h2>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Link
            to="/admin/companies"
            className="flex flex-col items-center p-4 bg-white rounded-lg hover:bg-gray-100 transition-all duration-300 hover:-translate-y-2 hover:shadow-md"
            style={{ backgroundColor: '#fff' }}
          >
            <div className="p-3 bg-[#4361ee]/10 rounded-full mb-2">
              <FiDatabase size={24} className="text-[#4361ee]" />
            </div>
            <span className="text-sm font-medium text-gray-700">Manage Companies</span>
          </Link>

          <Link
            to="/managers"
            className="flex flex-col items-center p-4 bg-white rounded-lg hover:bg-gray-100 transition-all duration-300 hover:-translate-y-2 hover:shadow-md"
            style={{ backgroundColor: '#fff' }}
          >
            <div className="p-3 bg-purple-500/10 rounded-full mb-2">
              <FiUserPlus size={24} className="text-purple-500" />
            </div>
            <span className="text-sm font-medium text-gray-700">Manage Managers</span>
          </Link>

          <Link
            to="/admin/Packages"
            className="flex flex-col items-center p-4 bg-white rounded-lg hover:bg-gray-100 transition-all duration-300 hover:-translate-y-2 hover:shadow-md"
            style={{ backgroundColor: '#fff' }}
          >
            <div className="p-3 bg-green-500/10 rounded-full mb-2">
              <FiPackage size={24} className="text-green-500" />
            </div>
            <span className="text-sm font-medium text-gray-700">Manage Packages</span>
          </Link>

          <Link
            to="/admin/permissions_groups"
            className="flex flex-col items-center p-4 bg-white rounded-lg hover:bg-gray-100 transition-all duration-300 hover:-translate-y-2 hover:shadow-md"
            style={{ backgroundColor: '#fff' }}
          >
            <div className="p-3 bg-amber-500/10 rounded-full mb-2">
              <FiShield size={24} className="text-amber-500" />
            </div>
            <span className="text-sm font-medium text-gray-700">Permissions</span>
          </Link>
        </div>
      </motion.div>
    </section>
  )
}
