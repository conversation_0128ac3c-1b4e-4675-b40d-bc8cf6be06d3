import { useMutation } from 'react-query';
import axiosInstance from '../config/Axios';

import { useGlobalContext } from '@contexts/GlobalContext';

const getDataTable = async (payload) => {
    let ordered_col = payload.sortField ? payload.sortField : 'created_at';
    // إذا كان هناك ? في url أضف & وإلا أضف ?
    let url = `/${payload?.url}`;
    let hasQuery = url.includes('?');
    url += hasQuery ? `&page=${payload.page + 1}&per_page=${payload.rows}` : `?page=${payload.page + 1}&per_page=${payload.rows}`;

    if (url.includes("datatable")) {
        url = `${url}&render_html=0`

        if (payload?.designID) {
            url = `${url}&design_id=${payload?.designID}`
        }
        if (payload?.groupID)
            url = `${url}&group_id=${payload?.groupID}`
    }

    const filteredFilters = Object.fromEntries(
        Object.entries(payload.filters).filter(([key, { value }]) => value !== '')
    );

    const pushed = {
        "order_by": {
            [ordered_col]: payload.sortOrder == 1 ? "desc" : 'asc'
        },
        "filters": filteredFilters,
        "filters_date": payload.filters_date,
    };

    // قبل تنفيذ الطلب اطبع الرابط والبيانات
    console.log('🟢 Final API URL:', url);
    console.log('🟢 Payload sent to backend:', pushed);
    const { data } = await axiosInstance.post(url, pushed);

    console.log("\ud83d\udccc API Response:", data);

    return data
}

export const useGetDataTable = () => {
    const { showToast } = useGlobalContext();

    return useMutation(getDataTable, {
        onError: (error) => showToast("error", "Error ", error.response?.data?.message)
    })
}

//-------------------------------------------------Delete DataTable Row-------------------------------------------------
const deleteRow = async (payload) => {
    const { data } = await axiosInstance.delete(`/datatable/${payload?.table}/${payload?.id}/delete`);

    return data.data
}

export const useDeleteDataTableRow = () => {
    const { showToast } = useGlobalContext();

    return useMutation(deleteRow, {
        onError: (error) => showToast("error", "Error ", error.response?.data?.message)
    })
}
