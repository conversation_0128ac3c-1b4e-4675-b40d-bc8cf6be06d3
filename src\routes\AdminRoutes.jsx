import { Route, Routes, Navigate } from 'react-router-dom';
import { useEffect, useState } from 'react';

import { DesignSpaceProvider } from '@contexts/DesignSpaceContext';

import SettingsIndex from '@dashboard/Setting';
import MembersIndex from '@dashboard/Users';
import EventsIndex from '@dashboard/Events';
import DesignIndex from '@dashboard/DesignSpace';
import CardsIndex from '@dashboard/Cards';
import AllCardsIndex from '@dashboard/All_Cards';
import OrginalBackagesIndex from '../pages/Dashboard/Backages/OrginalBackagesIndex';

import ManagersDataTable from '../pages/Dashboard/Users/<USER>';
import SoldBackagesIndex from '../pages/Dashboard/Backages/SoldBackagesIndex';
import PackagesHistory from '../pages/Dashboard/Backages/PackageHestory';
import BillingHistory from '../pages/Dashboard/Billing';
import Templates from '@dashboard/Templates';
import Companies from '@pages/Admin/Companies';
import Layout from '@pages/Layout';
import SalesManagementDashboard from '@dashboard/permissions_group';
import AdminDashboard from '@pages/Admin/Dashboard';


function AdminRoutes() {
    const [userRole, setUserRole] = useState(localStorage.getItem("user_role"));

    if (!userRole) {
        return <Navigate to='/login' replace />;
    }

    return (
        <Routes>
            <Route path='/admin/dashboard' element={<Layout><AdminDashboard /></Layout>} />
            <Route path='/admin/settings' element={<Layout><SettingsIndex /></Layout>} />
            <Route path='/admin/events' element={<Layout><EventsIndex /></Layout>} />
            <Route path='/admin/c_types' element={<Layout><CardsIndex /></Layout>} />  {/*Card Types, used this naming scheme instead of card_types to make the ".include" work as intended*/}
            <Route path='/admin/cards' element={<Layout><AllCardsIndex /></Layout>} />


            <Route path='/admin/Packages' element={<Layout><OrginalBackagesIndex /></Layout>} />

            <Route path='/admin/sold_P' element={<Layout><SoldBackagesIndex /></Layout>} /> {/*Same thing as the card naming scheme*/}

            <Route path="/managers" element={<Layout><ManagersDataTable /></Layout>} />


            <Route path="/:userId/packages-history" element={ <Layout><PackagesHistory /></Layout>   } />

            <Route path='/admin/companies' element={<Layout><Companies /></Layout>} />
            <Route path='/admin/permissions_groups' element={<Layout><SalesManagementDashboard /></Layout>} />
            <Route path='/admin/billing' element={<Layout><BillingHistory /></Layout>} />


            <Route path='/admin/design-space/:id?' element={
                <Layout>
                    <DesignSpaceProvider>
                        <DesignIndex />
                    </DesignSpaceProvider>
                </Layout>
            } />

            <Route path='/admin/template-design' element={
                <Layout>
                    <DesignSpaceProvider>
                        <Templates />
                    </DesignSpaceProvider>
                </Layout>
            } />
        </Routes>
    );
}

export default AdminRoutes;
