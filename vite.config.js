import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@dashboard": path.resolve(__dirname, "src/pages/Dashboard"),
      "@components": path.resolve(__dirname, "src/components"),
      "@constants": path.resolve(__dirname, "src/constants"),
      "@contexts": path.resolve(__dirname, "src/contexts"),
      "@images": path.resolve(__dirname, "src/assets/images"),
      "@quires": path.resolve(__dirname, "src/quires"),
      "@utils": path.resolve(__dirname, "src/utils"),
      "@pages": path.resolve(__dirname, "src/pages"),
      "@css": path.resolve(__dirname, "src/assets/css"),
     },
  },
  assetsInclude: ['**/*.glb'],
});
