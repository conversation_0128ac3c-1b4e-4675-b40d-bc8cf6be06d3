import React from 'react'
import SideMenu from './SideMenu'
import Banner from './Banner'
import { useLayout } from '../../contexts/LayoutContext'

function Layout({ children }) {
    const {
        getSidebarClasses,
        getMainContentClasses,
        isMobile,
        isSidebarCollapsed,
        isBurgerMenuOpen
    } = useLayout();

    return (
        <main className='flex h-[100vh] bg-[#e5e7eb]'>
            <aside className={`
                ${getSidebarClasses()}
                layout-sidebar
                ${isMobile ? (isBurgerMenuOpen ? 'mobile-open' : '') : (isSidebarCollapsed ? 'sidebar-collapsed' : 'sidebar-expanded')}
            `}>
                <SideMenu />
            </aside>
            <section className={`
                ${getMainContentClasses()}
                layout-main-content
                ${!isMobile && isSidebarCollapsed ? 'layout-main-expanded' : 'layout-main-normal'}
                flex flex-col
            `}>
                {/* <Banner />                                      Disabled Navbar */}

                <div className="w-full p-5 h-[95vh] overflow-y-auto">
                    {children}
                </div>
            </section>
        </main>
    )
}

export default Layout