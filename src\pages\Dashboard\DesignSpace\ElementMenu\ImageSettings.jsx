import { useState } from 'react';
import { isEmpty } from 'lodash';

import { useDesignSpace } from "@contexts/DesignSpaceContext";

import { RiImage2Line } from 'react-icons/ri';
import { Slider } from "primereact/slider";
import { Dialog } from 'primereact/dialog';
import LoadOnScroll from './LoadOnScroll';
import ImageEditor from '../components/ImageEditor';
import './ProfessionalImageGallery.css';

function ImageSettings() {
  const { addElement, selectedElement, setSelectedElement, setElements, elements } = useDesignSpace();
  const [refetch, setRefetch] = useState(true);
  const [showImageEditor, setShowImageEditor] = useState(false);

  const imageSettingHandler = (key, value) => {
    if (!selectedElement) return;

    setSelectedElement(prev => ({ ...prev, [key]: value }))
    const updatedElements = elements.map((el) => el.id === selectedElement.id ? { ...el, [key]: value } : el);
    setElements(updatedElements);
  }

  return (
    <>
      <div className="flex justify-start flex-wrap">
        <div className="w-full flex">
          <button className="w-full my-2 add-element-btn" onClick={() => addElement("img", "https://www.gravatar.com/avatar/?d=mp", { width: 200, height: 150 })}>
            <RiImage2Line size={24} className="mb-2" />
            <span>Add Member image</span>
          </button>

          {/* <button
            className={`w-4/12 me-2 my-2 add-element-btn ${!selectedElement || selectedElement.type !== 'img' ? 'opacity-50 cursor-not-allowed' : ''}`}
            onClick={openImageEditor}
            disabled={!selectedElement || selectedElement.type !== 'img'}
          >
            <RiImage2Line size={24} className="mb-2" />
            <span>Advanced Edit</span>
          </button> */}
        </div>

        <div className="w-full mt-5 m-2">
          

          <label className='mb-4 text-sm' htmlFor="borderRadius"> Border Radius: {selectedElement?.borderRadius}% </label>
          <Slider
            id="borderRadius"
            value={selectedElement?.borderRadius}
            onChange={(e) => imageSettingHandler("borderRadius", e.value)} // Update border-radius state
            min={0}
            max={100} // Range for border-radius (0% to 50%)
            className="my-3 mx-2"
            disabled={isEmpty(selectedElement)}
          />
        </div>

        <div className="professional-library-section w-full">
          <div className="professional-library-header">
            <h4 className="professional-library-title">
              <span className="professional-library-icon">🖼️</span>
              Members Image Library
            </h4>
            <div className="professional-library-subtitle">
              Choose from your beautiful collection
            </div>
          </div>
          <LoadOnScroll fileType="image" refetch={refetch} setRefetch={setRefetch} />
        </div>
      </div>

      {/* Image Editor Dialog */}
      <Dialog
        visible={showImageEditor}
        onHide={() => setShowImageEditor(false)}
        header="Advanced Image Editor"
        style={{ width: '90%', maxWidth: '800px' }}
        modal
        className="p-fluid"
      >
        {selectedElement && selectedElement.type === 'img' && (
          <ImageEditor
            imageId={selectedElement.id}
            onClose={() => setShowImageEditor(false)}
          />
        )}
      </Dialog>
    </>
  )
}

export default ImageSettings