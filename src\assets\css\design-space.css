/* Canva-style Design Space CSS */

/* Base styles */
:root {
  --canva-primary: #8b3dff;
  --canva-primary-hover: #7a2bff;
  --canva-secondary: #00c4cc;
  --canva-text: #333333;
  --canva-light-gray: #f5f5f5;
  --canva-gray: #e6e6e6;
  --canva-dark-gray: #666666;
  --canva-border: #dddddd;
  --canva-white: #ffffff;
  --canva-black: #000000;
  --canva-success: #00c48c;
  --canva-warning: #ffb237;
  --canva-danger: #ff5c5c;
}

body {
  overflow-x: hidden;
  font-family: 'Open Sans', 'Helvetica Neue', sans-serif;
  color: var(--canva-text);
}

/* Toolbar styles */
.canva-toolbar {
  background-color: var(--canva-white);
  border-bottom: 1px solid var(--canva-border);
  padding: 8px 16px;
  display: flex;
  align-items: center;
  height: 56px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.canva-toolbar button {
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--canva-text);
  transition: all 0.2s ease;
}

.canva-toolbar button:hover {
  background-color: var(--canva-light-gray);
}

.canva-toolbar button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Side menu styles */
.canva-side-menu {
  background-color: var(--canva-white);
  border-right: 1px solid var(--canva-border);
  height: 100%;
  width: 100%;
}

.canva-side-menu-header {
  padding: 12px 16px;
  border-bottom: 1px solid var(--canva-border);
}

.canva-side-menu-tabs {
  display: flex;
  overflow-x: auto;
  scrollbar-width: thin;
  border-bottom: 1px solid var(--canva-border);
}

.canva-side-menu-tabs::-webkit-scrollbar {
  height: 4px;
}

.canva-side-menu-tabs::-webkit-scrollbar-thumb {
  background-color: var(--canva-dark-gray);
  border-radius: 4px;
}

.canva-side-menu-content {
  padding: 16px;
  overflow-y: auto;
}

/* Element styles */
.resize-handle {
  background-color: #8b3dff;
  position: absolute;
  width: 12px;
  height: 12px;
  cursor: se-resize;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow:
    0 0 0 1px rgba(0, 0, 0, 0.2),
    0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 10;
  transition: all 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  opacity: 0.9;
}

.resize-handle::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 4px;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  transition: transform 0.2s ease;
}

.resize-handle:hover {
  transform: scale(1.3);
  background-color: #00c4cc;
  opacity: 1;
  box-shadow:
    0 0 0 1px rgba(0, 0, 0, 0.2),
    0 3px 6px rgba(0, 0, 0, 0.3),
    0 0 10px rgba(0, 196, 204, 0.4);
}


.resize-handle:hover::before {
  transform: translate(-50%, -50%) scale(1);
}

/* Rotation handle */
.rotation-handle {
  position: absolute;
  width: 14px;
  height: 14px;
  background-color: #00c4cc;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow:
    0 0 0 1px rgba(0, 0, 0, 0.2),
    0 2px 4px rgba(0, 0, 0, 0.2);
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  cursor: grab;
  z-index: 10;
  transition: all 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  opacity: 0;
}

.rotation-handle::before {
  content: '';
  position: absolute;
  top: 14px;
  left: 50%;
  height: 10px;
  width: 1px;
  background-color: rgba(0, 0, 0, 0.3);
  transform: translateX(-50%);
}

.rotation-handle::after {
  content: '↻';
  position: absolute;
  top: -5px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 10px;
  color: white;
}

.selected .rotation-handle {
  opacity: 1;
}

.rotation-handle:hover {
  transform: translateX(-50%) scale(1.2);
  background-color: #00d8e0;
  box-shadow:
    0 0 0 1px rgba(0, 0, 0, 0.2),
    0 3px 6px rgba(0, 0, 0, 0.3),
    0 0 10px rgba(0, 196, 204, 0.4);
}

/* Skew handles */
.skew-handle {
  position: absolute;
  width: 10px;
  height: 10px;
  background-color: #ff9500;
  border-radius: 2px;
  border: 2px solid white;
  box-shadow:
    0 0 0 1px rgba(0, 0, 0, 0.2),
    0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 10;
  transition: all 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  opacity: 0;
  transform: rotate(45deg);
}

.skew-handle.top {
  top: -5px;
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
  cursor: ns-resize;
}

.skew-handle.right {
  top: 50%;
  right: -5px;
  transform: translateY(-50%) rotate(45deg);
  cursor: ew-resize;
}

.skew-handle.bottom {
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
  cursor: ns-resize;
}

.skew-handle.left {
  top: 50%;
  left: -5px;
  transform: translateY(-50%) rotate(45deg);
  cursor: ew-resize;
}

.selected .skew-handle {
  opacity: 1;
}

.skew-handle:hover {
  transform: translateX(-50%) scale(1.3) rotate(45deg);
  background-color: #ffb340;
  box-shadow:
    0 0 0 1px rgba(0, 0, 0, 0.2),
    0 3px 6px rgba(0, 0, 0, 0.3),
    0 0 10px rgba(255, 149, 0, 0.4);
}

.skew-handle.right:hover, .skew-handle.left:hover {
  transform: translateY(-50%) scale(1.3) rotate(45deg);
}

.draggable-element {
  position: absolute;
  background-color: transparent;
  border: 1px solid transparent;
  cursor: grab;
  transition: all 0.2s ease;
}

.draggable-element:hover {
  border: 1px dashed var(--canva-dark-gray);
}

/* Element controls that appear on selection */
.element-controls {
  position: absolute;
  display: none;
  background: #2c3e50;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  padding: 6px;
  z-index: 100;
  align-items: center;
  gap: 4px;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Default position (top) */
.element-controls {
  flex-direction: row;
}

/* Position when element is near top edge */
.element-controls.top-edge {
  flex-direction: row;
}

/* Position when element is near right edge */
.element-controls.right-edge {
  flex-direction: column;
}

/* Position when element is near left edge */
.element-controls.left-edge {
  flex-direction: column;
}

/* Position when element is near both top and right edges */
.element-controls.top-edge.right-edge {
  flex-direction: column;
}

/* Position when element is near both top and left edges */
.element-controls.top-edge.left-edge {
  flex-direction: column;
}

.element-controls::after {
  content: '';
  position: absolute;
  width: 12px;
  height: 12px;
  background: #2c3e50;
  transform: rotate(45deg);
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.15);
  z-index: -1;
}

/* Arrow position for default (top) */
.element-controls::after {
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
}

/* Arrow position for top edge (when toolbar is at bottom) */
.element-controls.top-edge::after {
  top: -6px;
  bottom: auto;
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
}

/* Arrow position for right edge */
.element-controls.right-edge::after {
  right: -6px;
  left: auto;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
}

/* Arrow position for left edge */
.element-controls.left-edge::after {
  left: -6px;
  right: auto;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
}

/* Arrow position for both top and right edges */
.element-controls.top-edge.right-edge::after {
  right: -6px;
  left: auto;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
}

/* Arrow position for both top and left edges */
.element-controls.top-edge.left-edge::after {
  left: -6px;
  right: auto;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
}

.selected .element-controls {
  display: flex;
  transform: scale(1) translateY(0);
  opacity: 1;
}

.element-control-btn {
  min-width: 34px;
  height: 34px;
  border: none;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.9);
  transition: all 0.2s ease;
  position: relative;
  margin: 0 1px;
  flex-shrink: 0;
}

.element-control-btn svg {
  width: 18px;
  height: 18px;
  transition: all 0.2s ease;
}

.element-control-btn:hover {
  background-color: rgba(255, 255, 255, 0.25);
  color: white;
  transform: translateY(-2px);
}

.element-control-btn:active {
  transform: translateY(0);
  background-color: rgba(255, 255, 255, 0.2);
}

/* Rotate button */
.element-control-btn.rotate-btn {
  background-color: rgba(255, 255, 255, 0.15);
}

.element-control-btn.rotate-btn:hover {
  background-color: rgba(138, 61, 255, 0.7);
  color: white;
}

/* Forward button */
.element-control-btn.forward-btn {
  background-color: rgba(255, 255, 255, 0.15);
}

.element-control-btn.forward-btn:hover {
  background-color: rgba(0, 196, 204, 0.7);
  color: white;
}

/* Backward button */
.element-control-btn.backward-btn {
  background-color: rgba(255, 255, 255, 0.15);
}

.element-control-btn.backward-btn:hover {
  background-color: rgba(0, 196, 204, 0.7);
  color: white;
}

/* Delete button */
.element-control-btn.delete-btn {
  background-color: rgba(255, 255, 255, 0.15);
}

.element-control-btn.delete-btn:hover {
  background-color: rgba(255, 92, 92, 0.7);
  color: white;
}

/* Duplicate button */
.element-control-btn.duplicate-btn {
  background-color: rgba(255, 255, 255, 0.15);
}

.element-control-btn.duplicate-btn:hover {
  background-color: rgba(0, 196, 140, 0.7);
  color: white;
}

/* Tooltip for element controls */
.element-control-btn::after {
  content: attr(title);
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%) scale(0);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  white-space: nowrap;
  opacity: 0;
  transition: all 0.2s ease;
  pointer-events: none;
  z-index: 101;
}

/* Adjust tooltip position based on edge classes */
.element-control-btn::after {
  bottom: -6px;
  right: 15px;
}

/* Adjust tooltip position for right edge */
.element-controls.right-edge .element-control-btn::after {
  left: auto;
  right: -30px;
  top: 50%;
  bottom: auto;
  transform: translateY(-50%) scale(0);
}

.element-controls.right-edge .element-control-btn:hover::after {
  transform: translateY(-50%) scale(1);
  opacity: 1;
}

/* Ensure the controls container has enough space */
.element-controls {
  min-width: max-content;
  white-space: nowrap;
}

/* Add hover effect for vertical layout */
.element-controls.right-edge .element-control-btn:hover {
  transform: translateX(-2px);
}

.element-controls.right-edge .element-control-btn:active {
  transform: translateX(0);
}

/* Layer indicator */
.layer-indicator {
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--canva-primary);
  color: var(--canva-white);
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 10px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.selected .layer-indicator {
  opacity: 1;
}

.design-space {
  position: relative;
  overflow: hidden;
  background-color: #fff !important;
  background-image: none !important;
  background-size: initial !important;
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.2),
    0 0 8px rgba(0, 0, 0, 0.1),
    inset 0 0 0 1px rgba(255, 255, 255, 0.5);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border-radius: 8px;
  transform-style: preserve-3d;
  perspective: 1200px;
  transform: rotateX(2deg);
  backface-visibility: hidden;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.design-space::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: none !important;
  pointer-events: none;
  z-index: 1;
  border-radius: 8px;
}

.design-space::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 5%;
  right: 5%;
  height: 15px;
  background-color: rgba(0, 0, 0, 0.15);
  filter: blur(8px);
  border-radius: 50%;
  z-index: -1;
  transform-origin: center;
  animation: shadow-pulse 5s ease-in-out infinite alternate;
}

@keyframes shadow-pulse {
  0% {
    transform: scaleX(0.95);
    opacity: 0.15;
  }
  100% {
    transform: scaleX(1);
    opacity: 0.2;
  }
}

.design-space > div {
  position: absolute;
}

.design-space:hover {
  transform: rotateX(0deg) translateY(-3px);
  box-shadow:    0 20px 40px rgba(0, 0, 0, 0.25),    0 0 15px rgba(0, 0, 0, 0.1),    inset 0 0 0 1px rgba(255, 255, 255, 0.6);
  -webkit-transform: rotateX(0deg) translateY(-3px);
  -moz-transform: rotateX(0deg) translateY(-3px);
  -ms-transform: rotateX(0deg) translateY(-3px);
  -o-transform: rotateX(0deg) translateY(-3px);
}

/* Professional corner marks */
.design-space::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 8px;
  pointer-events: none;
  z-index: 2;
}

.design-space::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 5%;
  right: 5%;
  height: 15px;
  background-color: rgba(0, 0, 0, 0.15);
  filter: blur(8px);
  border-radius: 50%;
  z-index: -1;
}

/* Corner marks */
.design-space .corner-mark {
  position: absolute;
  width: 15px;
  height: 15px;
  border: 2px solid rgba(0, 0, 0, 0.2);
  pointer-events: none;
  z-index: 2;
}

.design-space .corner-mark.top-left {
  top: 5px;
  left: 5px;
  border-right: none;
  border-bottom: none;
}

.design-space .corner-mark.top-right {
  top: 5px;
  right: 5px;
  border-left: none;
  border-bottom: none;
}

.design-space .corner-mark.bottom-left {
  bottom: 5px;
  left: 5px;
  border-right: none;
  border-top: none;
}

.design-space .corner-mark.bottom-right {
  bottom: 5px;
  right: 5px;
  border-left: none;
  border-top: none;
}

.selected {
  border: 2px solid var(--canva-primary) !important;
  box-shadow: 0 0 0 1px var(--canva-primary), 0 0 8px rgba(139, 61, 255, 0.3);
}

/* Alignment controls */
.alignment-container button {
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid var(--canva-border);
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: var(--canva-white);
  color: var(--canva-text);
  padding: 8px;
  border-radius: 4px;
}

.alignment-container button:hover {
  background-color: var(--canva-light-gray);
}

.alignment-container button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Element buttons */
.elements-btns-container button {
  background-color: var(--canva-primary);
  border-radius: 4px;
  transition: all 0.2s ease;
  padding: 8px 16px;
  width: 100%;
  color: var(--canva-white);
  border: none;
  font-weight: 500;
  cursor: pointer;
}

.elements-btns-container button:hover {
  background-color: var(--canva-primary-hover);
}

/* Add element button */
.add-element-btn {
  padding: 16px;
  border: 2px dashed var(--canva-dark-gray);
  color: var(--canva-dark-gray);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
  border-radius: 4px;
  cursor: pointer;
}

.add-element-btn:hover {
  color: var(--canva-text);
  background-color: var(--canva-light-gray);
  border-color: var(--canva-text);
}

/* Animations */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.3;
  }
}

/* Table styles */
.sticky-header {
  position: sticky;
  top: 0;
  background-color: var(--canva-white);
  z-index: 50;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.sticky-header thead th {
  position: sticky;
  top: 0;
  background: var(--canva-white);
  z-index: 10;
  box-shadow: 0 2px 2px -1px rgba(0,0,0,0.1);
}

.table-responsive {
  overflow-y: auto;
  height: calc(100vh - 300px);
}

/* Canva-specific components */
.canva-templates h3,
.canva-elements h3,
.canva-photos h3,
.canva-videos h3,
.canva-uploads h3,
.canva-backgrounds h3,
.canva-charts h3,
.canva-tables h3 {
  font-weight: 600;
  margin-bottom: 16px;
  color: var(--canva-text);
}

/* Search inputs */
.canva-side-menu input[type="text"] {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--canva-border);
  border-radius: 4px;
  font-size: 14px;
  transition: border 0.2s ease;
}

.canva-side-menu input[type="text"]:focus {
  border-color: var(--canva-primary);
  outline: none;
  box-shadow: 0 0 0 2px rgba(139, 61, 255, 0.2);
}

/* Dropdown menus */
.canva-toolbar .dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: var(--canva-white);
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 100;
  min-width: 180px;
  padding: 8px 0;
}

.canva-toolbar .dropdown-menu li {
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.canva-toolbar .dropdown-menu li:hover {
  background-color: var(--canva-light-gray);
}

/* Zoom controls */
.zoom-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.zoom-percentage {
  font-size: 14px;
  font-weight: 500;
  min-width: 48px;
  text-align: center;
}

/* Layer controls */
.layer-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Group controls */
.group-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}




.text-assistant {
  pointer-events: auto !important;
}

.text-assistant * {
  pointer-events: auto !important;
}

.text-assistant .p-button {
  pointer-events: auto !important;
}

.text-assistant .p-dropdown {
  pointer-events: auto !important;
}

.text-assistant .p-slider {
  pointer-events: auto !important;
}

.text-assistant input[type="color"] {
  pointer-events: auto !important;
}

.text-assistant .p-inputtext {
  pointer-events: auto !important;
}

/* تحسين مظهر الأزرار */
.text-assistant .p-button.p-button-primary {
  background-color: #4338ca !important;
  border-color: #4338ca !important;
  color: white !important;
}

.text-assistant .p-button.p-button-primary:hover {
  background-color: #3730a3 !important;
  border-color: #3730a3 !important;
}

.text-assistant .p-button.p-button-outlined {
  background-color: transparent !important;
}

.text-assistant .p-button.p-button-outlined:hover {
  background-color: rgba(67, 56, 202, 0.1) !important;
}

/* تحسين مظهر الأزرار المعطلة */
.text-assistant .p-button:disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
}

/* تحسين مظهر الألوان */
.text-assistant input[type="color"] {
  border: 2px solid #e5e7eb !important;
  border-radius: 4px !important;
  cursor: pointer !important;
}

.text-assistant input[type="color"]:hover {
  border-color: #d1d5db !important;
}

/* تحسين مظهر القوائم المنسدلة */
.text-assistant .p-dropdown {
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
}

.text-assistant .p-dropdown:hover {
  border-color: #9ca3af !important;
}

/* تحسين مظهر حقول الإدخال */
.text-assistant .p-inputtext {
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
}

.text-assistant .p-inputtext:focus {
  border-color: #4338ca !important;
  box-shadow: 0 0 0 3px rgba(67, 56, 202, 0.1) !important;
}

/* تحسين مظهر الشرائح */
.text-assistant .p-slider {
  margin: 0.5rem 0 !important;
}

.text-assistant .p-slider .p-slider-handle {
  background-color: #4338ca !important;
  border: 2px solid white !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.text-assistant .p-slider .p-slider-range {
  background-color: #4338ca !important;
}

/* تحسين مظهر منطقة المعاينة */
.text-assistant .preview-area {
  border: 2px solid #e5e7eb !important;
  border-radius: 8px !important;
  background-color: white !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
}

/* تحسين مظهر التعليمات */
.text-assistant .instructions-panel {
  background-color: #eff6ff !important;
  border: 1px solid #bfdbfe !important;
  border-radius: 8px !important;
}

/* تحسين مظهر العناوين */
.text-assistant h4 {
  color: #374151 !important;
  font-weight: 600 !important;
  margin-bottom: 0.5rem !important;
}

/* تحسين مظهر التسميات */
.text-assistant label {
  color: #4b5563 !important;
  font-weight: 500 !important;
  margin-bottom: 0.25rem !important;
}

/* تحسين مظهر الشبكة */
.text-assistant .grid {
  gap: 0.75rem !important;
}

/* تحسين مظهر الأزرار في الشبكة */
.text-assistant .grid .p-button {
  font-size: 0.875rem !important;
  padding: 0.5rem 0.75rem !important;
}

/* تحسين مظهر الأزرار المحددة */
.text-assistant .p-button.p-button-outlined.p-button-success {
  color: #10b981 !important;
  border-color: #10b981 !important;
  background-color: transparent !important;
}

.text-assistant .p-button.p-button-outlined.p-button-success:hover {
  background-color: #10b981 !important;
  color: white !important;
}

.text-assistant .p-button.p-button-outlined.p-button-success:disabled {
  color: #9ca3af !important;
  border-color: #9ca3af !important;
  background-color: transparent !important;
}

/* ضمان عمل الخطوط العربية */
@font-face {
  font-family: 'Cairo';
  src: url('https://fonts.gstatic.com/s/cairo/v20/SLXVc1nY6HkvangtZmpcWmhzfH5lWWgcQyyS4J0.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Cairo';
  src: url('https://fonts.gstatic.com/s/cairo/v20/SLXVc1nY6HkvangtZmpcWmhzfH5lWWgcQyyS4J0.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Tajawal';
  src: url('https://fonts.gstatic.com/s/tajawal/v8/Iura6YBj_oCad4k1r_5qA.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Tajawal';
  src: url('https://fonts.gstatic.com/s/tajawal/v8/Iura6YBj_oCad4k1r_5qA.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/* ضمان عمل الخطوط الإنجليزية */
@font-face {
  font-family: 'Roboto';
  src: url('https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu4mxK.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Playfair Display';
  src: url('https://fonts.gstatic.com/s/playfairdisplay/v30/nuFvD-vYSZviVYUb_rj3ij__anPXJzDwcbmjWBN2PKdFvXDXbtXK-F2qC0s.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Pacifico';
  src: url('https://fonts.gstatic.com/s/pacifico/v22/FwZY7-Qmy14u9lezJ-6H6MmBp0u-.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Bebas Neue';
  src: url('https://fonts.gstatic.com/s/bebasneue/v9/JTUSjIg69CK48gW7PXoo9WlhI.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Dancing Script';
  src: url('https://fonts.gstatic.com/s/dancingscript/v24/If2cXTr6YS-zF4S-kcSWSVi_sxjsohD9F50Ruu7BMSo3ROp6.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Orbitron';
  src: url('https://fonts.gstatic.com/s/orbitron/v25/yMJMMIlzdpvBhQQL_SC3X9yhF25-T1nyGy6BoWgz.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Lobster';
  src: url('https://fonts.gstatic.com/s/lobster/v28/neILzCirqoswsqX9zoKmNg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Abril Fatface';
  src: url('https://fonts.gstatic.com/s/abrilfatface/v12/zOL64pLDpnLkHmFHMKNTQ4g5bs3t6GqJKxeA.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Righteous';
  src: url('https://fonts.gstatic.com/s/righteous/v9/1cXxaUPXBpj2rGoU7C9WiHGA.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Permanent Marker';
  src: url('https://fonts.gstatic.com/s/permanentmarker/v10/Fh4uPib6I9yqygr9j2ePTWi4QKqyC6.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Fredoka One';
  src: url('https://fonts.gstatic.com/s/fredokaone/v8/k3kUo8kEI-tA1RRcTZGmTlHGCaI.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Bangers';
  src: url('https://fonts.gstatic.com/s/bangers/v12/FeVQS0BTqb0h60ACH55Q2J5hm.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Chewy';
  src: url('https://fonts.gstatic.com/s/chewy/v12/uK_94ruUb-k-wn52KjI.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Kaushan Script';
  src: url('https://fonts.gstatic.com/s/kaushanscript/v14/vm8vdRfvXFLG3OLnsO15WYS5DG74wNc.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Satisfy';
  src: url('https://fonts.gstatic.com/s/satisfy/v12/rP2Hp2yn6lkG50LoCZOIHQ.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Great Vibes';
  src: url('https://fonts.gstatic.com/s/greatvibes/v12/RWmMoLWRv4ITMsfS8c0tPvQ.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Cinzel';
  src: url('https://fonts.gstatic.com/s/cinzel/v16/8vIU7ww63mVu7gtR-kwKxNvkNOjw-tbnTYrvTO5c4A.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'UnifrakturMaguntia';
  src: url('https://fonts.gstatic.com/s/unifrakturmaguntia/v12/WWXPlieVYwiGNomYU-ciRLRvEmK7oaVem2ZI.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Creepster';
  src: url('https://fonts.gstatic.com/s/creepster/v12/AlZy_zVUqJz4yMrniH4Rcn35.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Faster One';
  src: url('https://fonts.gstatic.com/s/fasterone/v12/H4ciBXCHmdfClFd-vWhxXPX5.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Press Start 2P';
  src: url('https://fonts.gstatic.com/s/pressstart2p/v14/e3t4euO8T-267oIAQAu6jDQyK3nVivM.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'VT323';
  src: url('https://fonts.gstatic.com/s/vt323/v12/pxiKyp0ihIEF2isfFJU.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Share Tech Mono';
  src: url('https://fonts.gstatic.com/s/sharetechmono/v10/J7aHnp1uDWRBEqV98dVQztYldFcLowEF.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Space Mono';
  src: url('https://fonts.gstatic.com/s/spacemono/v10/i7dPIFZifjKcF5UAWdDRYEF8RQ.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Major Mono Display';
  src: url('https://fonts.gstatic.com/s/majormonodisplay/v6/RWmVoLyb5fEqtsfBX9PDZIGr2tFubRhLCn2QIndPww.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Crimson Text';
  src: url('https://fonts.gstatic.com/s/crimsontext/v19/wlp2gwHKFkZgtmSR3NB0oRJfbwhT.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Libre Baskerville';
  src: url('https://fonts.gstatic.com/s/librebaskerville/v14/kmKnZrc3Hgbbcjq75U4uslyuy4kn0qNZaxY.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Lora';
  src: url('https://fonts.gstatic.com/s/lora/v26/0QI6MX1D_JOuGQbT0gvTJPa787weuyJGmKM.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Source Sans Pro';
  src: url('https://fonts.gstatic.com/s/sourcesanspro/v21/6xK3dSBYKcSV-LCoeQqfX1RYOo3qOK7l.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('https://fonts.gstatic.com/s/nunito/v24/XRXI3I6Li01BKofiOc5wtlZ2di8HDLshdTQ3jw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Quicksand';
  src: url('https://fonts.gstatic.com/s/quicksand/v29/6xK-dSZaM9iE8KbpRA_LJ3z8mH9BOJvgkP8o58a-xw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Comfortaa';
  src: url('https://fonts.gstatic.com/s/comfortaa/v37/1Pt_g8LJRfWJmhDAuUsSQamb1W0lwk4S4TbMDrMfJQ.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Varela Round';
  src: url('https://fonts.gstatic.com/s/varelaround/v13/w8gdH283Tvk__Lua32TysjIfp8uPLdshZg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Maven Pro';
  src: url('https://fonts.gstatic.com/s/mavenpro/v32/7Auup_AqnyWWAxW2Wk3swUz56MS91Eww8SX25nCpkp4.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Exo 2';
  src: url('https://fonts.gstatic.com/s/exo2/v20/7cH1v4okm5zmbvwkAx_sfcEuiD8jWfWcPg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Rajdhani';
  src: url('https://fonts.gstatic.com/s/rajdhani/v15/LDI2apCSOBg7S-QT7paQc6M.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Titillium Web';
  src: url('https://fonts.gstatic.com/s/titilliumweb/v15/NaPecZTIAOhVxoMyOr9n_E7fdMPmCA.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Josefin Sans';
  src: url('https://fonts.gstatic.com/s/josefinsans/v25/Qw3PZQNVED7rKGKxtqIqX5E-AVSJrOCfjY46_DjQbMZhKg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Abel';
  src: url('https://fonts.gstatic.com/s/abel/v18/MwQ5bhbm2POE6VhLPw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Anton';
  src: url('https://fonts.gstatic.com/s/anton/v23/1Ptgg87LROyAm3Kz-Co.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Bungee';
  src: url('https://fonts.gstatic.com/s/bungee/v6/N0bU2SZBIuF2PU_0AnR1Gd8.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Black Ops One';
  src: url('https://fonts.gstatic.com/s/blackopsone/v12/qWcsB6-ypo7xBdr6Xshe96H3aDbbtwkh.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Russo One';
  src: url('https://fonts.gstatic.com/s/russoone/v14/Z9XUDmZRWg6M1LvRYsHOz8mJ.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Audiowide';
  src: url('https://fonts.gstatic.com/s/audiowide/v8/l7gdbjpo0cum0ckerWCtkQ.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Changa One';
  src: url('https://fonts.gstatic.com/s/changaone/v13/xfu00W3wXn3QLUJXhzq46AbouLfbK64.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Sniglet';
  src: url('https://fonts.gstatic.com/s/sniglet/v13/cIf9MaFLtkE2UupGgQYhiA.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Boogaloo';
  src: url('https://fonts.gstatic.com/s/boogaloo/v12/kmK-Zq45GAvOdnaW6x1F_SrQo.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Bubblegum Sans';
  src: url('https://fonts.gstatic.com/s/bubblegumsans/v12/AYCSpXb_Z9EORv1M5QTjEzMEtdaHzoPPbqR4.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Cherry Cream Soda';
  src: url('https://fonts.gstatic.com/s/cherrycreamsoda/v12/UMBIrOxBrW6w2FFyi9paG0fdVdRciQd6A4Q.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Freckle Face';
  src: url('https://fonts.gstatic.com/s/freckleface/v9/AMOWz4SXrmKHCvXTohxY-YI0Uw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gloria Hallelujah';
  src: url('https://fonts.gstatic.com/s/gloriahallelujah/v12/LYjYdHv3pUkNBMypJh7elzXKCM41xtdECq76mk.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Indie Flower';
  src: url('https://fonts.gstatic.com/s/indieflower/v17/m8JVjfNVeKWVnh3QMuKkFcZVaUuC.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Kalam';
  src: url('https://fonts.gstatic.com/s/kalam/v11/YA9Qr0Wd4kDdMtD6GgLLmCUItqGt.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Patrick Hand';
  src: url('https://fonts.gstatic.com/s/patrickhand/v14/Ln1FzOA-y6TkwHrOUc6NnUjKRp9m.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Reenie Beanie';
  src: url('https://fonts.gstatic.com/s/reeniebeanie/v11/z7NSdR76eDkaJKZJFkkjuvWxXPq1qw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Rock Salt';
  src: url('https://fonts.gstatic.com/s/rocksalt/v11/MwQ0bhv11fDH6wL6ZCL4I8Q.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Shadows Into Light';
  src: url('https://fonts.gstatic.com/s/shadowsintolight/v12/UqyNK9UOIntux_czAvDQx_ZcHqZXBNQDcg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Special Elite';
  src: url('https://fonts.gstatic.com/s/specialelite/v11/XLYgIZbkc4JPUL5CVArUVL0ntnAOTQ.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Walter Turncoat';
  src: url('https://fonts.gstatic.com/s/walterturncoat/v12/snfys0Gs98ln43n0d-14ULoToe67YB2M.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

/* ضمان عمل الخطوط العربية الجديدة */
@font-face {
  font-family: 'Scheherazade New';
  src: url('https://fonts.gstatic.com/s/scheherazadenew/v4/4UaBrE6tmq0gO-tVs9Ipr5-9-mbRvNUF2I.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Lateef';
  src: url('https://fonts.gstatic.com/s/lateef/v17/hESw6XVnNCxEvkbMpheEZo_H_w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Harmattan';
  src: url('https://fonts.gstatic.com/s/harmattan/v8/gokpH6L2DkFvVvRp9XpTS0CjkP1Yog.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'El Messiri';
  src: url('https://fonts.gstatic.com/s/elmessiri/v12/K2F0fZBRmr9vQ1pHEey6GIGo8_pv3myYjuXCe65ghj3OTw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Aref Ruqaa';
  src: url('https://fonts.gstatic.com/s/arefruqaa/v12/WwkbxPW1E165DjQ5VsZzqN5NjF7Nw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Readex Pro';
  src: url('https://fonts.gstatic.com/s/readexpro/v1/SLXYc1bJ7HE5YDoGPuzj_dh8na74Kiw.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'IBM Plex Sans Arabic';
  src: url('https://fonts.gstatic.com/s/ibmplexsansarabic/v9/Qw3CZRtWPQCuHme67tEYUIx3Kh0SHR6x6jOjR9VfPcrKtuJ2Jplg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans Arabic';
  src: url('https://fonts.gstatic.com/s/notosansarabic/v18/nwpCt6W9KfF6gV1yPu9T3JqRBNbE8tq1lx20.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif Arabic';
  src: url('https://fonts.gstatic.com/s/notoserifarabic/v18/ga6Iaw1J5X9T9RW6j9bNVlsKbJovrb0b8.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Alkalami';
  src: url('https://fonts.gstatic.com/s/alkalami/v1/zOL-4pbPn6Im26Ke4HOxT7Y.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Anek Arabic';
  src: url('https://fonts.gstatic.com/s/anekarabic/v1/5aUz9_-1phKLFgshYDvh6O4hp3wSa0b4.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'IBM Plex Serif Arabic';
  src: url('https://fonts.gstatic.com/s/ibmplexserifarabic/v9/Qw3CZRtWPQCuHme67tEYUIx3Kh0SHR6x6jOjR9VfPcrKtuJ2Jplg.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Nastaliq Urdu';
  src: url('https://fonts.gstatic.com/s/notonastaliqurdu/v1/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Rashi Hebrew';
  src: url('https://fonts.gstatic.com/s/notorashihebrew/v1/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans Hebrew';
  src: url('https://fonts.gstatic.com/s/notosanshebrew/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif Hebrew';
  src: url('https://fonts.gstatic.com/s/notoserifhebrew/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans Devanagari';
  src: url('https://fonts.gstatic.com/s/notosansdevanagari/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif Devanagari';
  src: url('https://fonts.gstatic.com/s/notoserifdevanagari/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans Bengali';
  src: url('https://fonts.gstatic.com/s/notosansbengali/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif Bengali';
  src: url('https://fonts.gstatic.com/s/notoserifbengali/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans Thai';
  src: url('https://fonts.gstatic.com/s/notosansthai/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif Thai';
  src: url('https://fonts.gstatic.com/s/notoserifthai/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans Korean';
  src: url('https://fonts.gstatic.com/s/notosanskorean/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif Korean';
  src: url('https://fonts.gstatic.com/s/notoserifkorean/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans Japanese';
  src: url('https://fonts.gstatic.com/s/notosansjapanese/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Noto Serif Japanese';
  src: url('https://fonts.gstatic.com/s/notoserifjapanese/v18/ieVc2YdFI3GCY6SyQy1KfStzYKZgzN1z0w.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

/* تحسين مظهر الشبكة */
.text-assistant .grid {
  gap: 0.75rem !important;
}

/* تحسين مظهر الأزرار في الشبكة */
.text-assistant .grid .p-button {
  font-size: 0.875rem !important;
  padding: 0.5rem 0.75rem !important;
}

/* تحسين مظهر الأزرار المحددة */
.text-assistant .p-button.p-button-outlined.p-button-success {
  color: #10b981 !important;
  border-color: #10b981 !important;
  background-color: transparent !important;
}

.text-assistant .p-button.p-button-outlined.p-button-success:hover {
  background-color: #10b981 !important;
  color: white !important;
}

.text-assistant .p-button.p-button-outlined.p-button-success:disabled {
  color: #9ca3af !important;
  border-color: #9ca3af !important;
  background-color: transparent !important;
}

/* تحسين أداء قائمة الخطوط */
.font-dropdown-panel {
  max-height: 300px !important;
  overflow-y: auto !important;
}

.font-dropdown-panel .p-dropdown-items {
  max-height: 250px !important;
}

.font-dropdown-panel .p-dropdown-item {
  padding: 0.5rem 0.75rem !important;
  font-size: 0.875rem !important;
  border-bottom: 1px solid #f3f4f6 !important;
}

.font-dropdown-panel .p-dropdown-item:hover {
  background-color: #f9fafb !important;
}

.font-dropdown-panel .p-dropdown-item.p-highlight {
  background-color: #3b82f6 !important;
  color: white !important;
}

/* تحسين مظهر البحث في قائمة الخطوط */
.font-dropdown-panel .p-dropdown-filter {
  padding: 0.5rem !important;
  border-bottom: 1px solid #e5e7eb !important;
  margin-bottom: 0.5rem !important;
}

.font-dropdown-panel .p-dropdown-filter-input {
  width: 100% !important;
  padding: 0.5rem !important;
  border: 1px solid #d1d5db !important;
  border-radius: 0.375rem !important;
  font-size: 0.875rem !important;
}

/* تحسين أداء Virtual Scrolling */
.font-dropdown-panel .p-virtualscroller {
  max-height: 200px !important;
}

.font-dropdown-panel .p-virtualscroller-content {
  padding: 0 !important;
}

/* تحسين مظهر مثال الخط */
.font-dropdown-panel .p-dropdown-item span:last-child {
  font-size: 0.75rem !important;
  opacity: 0.7 !important;
  margin-left: 0.5rem !important;
}

/* تحسين أداء التحميل */
.text-assistant .p-dropdown.p-component {
  transition: all 0.2s ease !important;
}

.text-assistant .p-dropdown.p-component:not(.p-disabled):hover {
  border-color: #3b82f6 !important;
}

.text-assistant .p-dropdown.p-component.p-focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}