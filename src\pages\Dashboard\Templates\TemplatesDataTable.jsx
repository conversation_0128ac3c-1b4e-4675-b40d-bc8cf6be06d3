import React, { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom';
import PropTypes from 'prop-types';
import parse from 'html-react-parser';

import { designsTableConfig, defaultTableConfig } from '@constants';
import { useDataTableContext } from '@contexts/DataTableContext';
import { useDeleteTemplate, useUpdateTemplateNameMutation, useGetCardsTypes } from '@quires/template';
import { useLayout } from '@contexts/LayoutContext';

import { TfiTrash } from "react-icons/tfi";
import { FiEdit } from 'react-icons/fi';
import { FaSearch } from 'react-icons/fa';
import { HiDotsVertical } from 'react-icons/hi';
import { Dropdown } from 'primereact/dropdown';

import { createPortal } from 'react-dom';
import { confirmDialog, ConfirmDialog } from 'primereact/confirmdialog';
import { Toast } from 'primereact/toast';
import { Dialog } from 'primereact/dialog';
import { Button } from 'primereact/button';
import Container from '@components/Container';
import axiosInstance from '../../../config/Axios';
import { motion } from 'framer-motion';
import WebFont from 'webfontloader';

const CARD_WIDTH = 240;
const CARD_HEIGHT = 416;
const CARD_CONTAINER_MAX_WIDTH = 280;
const CARD_CONTAINER_MAX_HEIGHT = 420;

function getScale(containerW, containerH, contentW, contentH) {
    return Math.min(containerW / contentW, containerH / contentH, 1);
}

// دالة لإخفاء الأقواس {{ }} فقط عند العرض
function hideBraces(html) {
    // استبدال {{variable}} بـ <span class="var-placeholder">$1</span> بدون أي ستايل خاص
    return html.replace(/{{\s*(.*?)\s*}}/g, '<span class="var-placeholder">$1</span>');
}

function TemplatesDataTable() {
    const { totalRecords, lazyParams, setLazyParams, data, dataHandler, loading } = useDataTableContext();
    const { isMobile } = useLayout();
    const navigate = useNavigate();

    const deleteTemplate = useDeleteTemplate()
    const updateTemplateName = useUpdateTemplateNameMutation();
    const [searchQuery, setSearchQuery] = useState('');
    const [cardTypeFilter, setCardTypeFilter] = useState('all');
    const { isLoading: cardTypesLoading, data: cardTypesData, error: cardTypesError } = useGetCardsTypes();
    const [mobileActionMenuOpen, setMobileActionMenuOpen] = useState(null);
    const [toastRef] = useState(React.createRef());
    const [selectedTemplate, setSelectedTemplate] = useState(null);
    const [templateModalVisible, setTemplateModalVisible] = useState(false);
    const [templateZoomLevel, setTemplateZoomLevel] = useState(1.0);
    const [editNameModal, setEditNameModal] = useState({ open: false, template: null });
    const [newTemplateName, setNewTemplateName] = useState('');
    const [fontsLoaded, setFontsLoaded] = useState(true); // افتراضيًا true حتى لا يمنع الرندر إلا في المعاينة

    // الآن فقط نضع console.log
    console.log('TemplatesDataTable render', 'selectedTemplate:', selectedTemplate, 'templateModalVisible:', templateModalVisible);

    // دالة لاستخراج جميع الخطوط من كود HTML التصميم باستخدام DOMParser
    function extractFontsFromHtml(html) {
        const fonts = new Set();
        if (!html) return [];
        try {
            const parser = new window.DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            doc.querySelectorAll('[style]').forEach(el => {
                const style = el.getAttribute('style');
                const match = /font-family:\s*([^;]+)/i.exec(style);
                if (match && match[1]) {
                    match[1].split(',').forEach(f => {
                        const clean = f.trim().replace(/['"]/g, '');
                        const mainFont = clean.split(',')[0].trim(); 
                        if (mainFont) fonts.add(mainFont);
                    });
                }
            });
        } catch (e) {}
        return Array.from(fonts);
    }

    useEffect(() => {
        console.log('useEffect: templateModalVisible', templateModalVisible, 'selectedTemplate', selectedTemplate);
        if (!templateModalVisible || !selectedTemplate) {
            setFontsLoaded(true);
            return;
        }
        console.log('selectedTemplate value in useEffect:', selectedTemplate);
        const fonts = extractFontsFromHtml(selectedTemplate);
        console.log('Extracted fonts:', fonts);
        if (fonts.length === 0) {
            setFontsLoaded(true);
            return;
        }
        setFontsLoaded(false);
        WebFont.load({
            google: {
                families: fonts,
            },
            active: () => setFontsLoaded(true),
            inactive: () => setFontsLoaded(true),
        });
    }, [templateModalVisible, selectedTemplate]);

    useEffect(() => {
        if (!data || !Array.isArray(data)) return;
        const allFonts = new Set();
        data.forEach(templateObj => {
            if (templateObj.template) {
                extractFontsFromHtml(templateObj.template).forEach(font => allFonts.add(font));
            }
        });
        const fontsArr = Array.from(allFonts);
        if (fontsArr.length > 0) {
            WebFont.load({
                google: {
                    families: fontsArr,
                }
            });
            console.log('Loaded fonts for all templates:', fontsArr);
        }
    }, [data]);

    // اشتراك
    const [subscriptionError, setSubscriptionError] = useState(null);
    const [subscriptionLoading, setSubscriptionLoading] = useState(true);
    const [noPackage, setNoPackage] = useState(false);
    useEffect(() => {
        const checkSubscription = async () => {
            try {
                const token = localStorage.getItem('token');
                const userId = localStorage.getItem('user_id');
                if (!token || !userId) {
                    setSubscriptionError({ message: 'User not authenticated.' });
                    setSubscriptionLoading(false);
                    return;
                }
                const response = await axiosInstance.get(`packages/show-package-by-id/${userId}`, {
                    headers: { Authorization: `Bearer ${token}` }
                });
                // إذا لم توجد بيانات باقة للمستخدم أو كانت فارغة
                if (!response.data || Object.keys(response.data).length === 0 || response.data.error === 'No package found for this user') {
                    setNoPackage(true);
                } else {
                    setNoPackage(false);
                }
                setSubscriptionError(null);
            } catch (error) {

                console.log('API error message:', error.response?.data?.error);
                if (error.response && error.response.data) {
                    const errMsg = error.response.data.error?.toLowerCase() || '';
                    if (error.response.data.error === "Your subscription has expired. Please renew your subscription to continue.") {
                        setSubscriptionError({ message: error.response.data.error });
                    } else if (
                        errMsg.includes('not found') ||
                        errMsg.includes('no package') ||
                        errMsg.includes('no active package found for this user') ||
                        errMsg.includes('must have an active package')
                    ) {
                        setNoPackage(true);
                    } else {
                        setSubscriptionError(null);
                    }
                } else {
                    setSubscriptionError(null);
                }
            } finally {
                setSubscriptionLoading(false);
            }
        };
        checkSubscription();
    }, []);

    useEffect(() => {
        setLazyParams({
            ...defaultTableConfig,
            ...designsTableConfig,
            rows: 12,
            url: 'get-designs-list?sort=-created_at'
        });
    }, [])

    // Get card types from API for filter options
    const cardTypeOptions = React.useMemo(() => {
        if (cardTypesLoading) return [{ label: 'Loading...', value: 'all', disabled: true }];
        if (cardTypesError) return [{ label: 'Error loading types', value: 'all', disabled: true }];
        if (!cardTypesData || cardTypesData.length === 0) return [{ label: 'All Types', value: 'all' }];
        return [
            { label: 'All Types', value: 'all' },
            ...cardTypesData.map(type => ({ label: type.name, value: type.id }))
        ];
    }, [cardTypesData, cardTypesLoading, cardTypesError]);

    // Add debounced search handler for API calls (keeping original functionality)
    useEffect(() => {
        const timeout = setTimeout(() => {
            // Build query string for filters
            let filterQuery = '';
            if (searchQuery.trim()) {
                filterQuery += `filter[name]=${encodeURIComponent(searchQuery.trim())}`;
            }
            if (cardTypeFilter !== 'all') {
                if (filterQuery.length > 0) filterQuery += '&';
                filterQuery += `filter[card_type_id]=${encodeURIComponent(cardTypeFilter)}`;
            }
            if (filterQuery.length > 0) filterQuery += '&';
            filterQuery += 'sort=-created_at';
            // Compose the url
            let url = 'get-designs-list';
            if (filterQuery.length > 0) {
                url += `?${filterQuery}`;
            }
            setLazyParams(prev => ({
                ...prev,
                url: url,
                rows: 12,
            }));
        }, 300);

        return () => clearTimeout(timeout);
    }, [searchQuery, cardTypeFilter, setLazyParams]);

    // --- ConfirmDialog handler for delete ---
    const confirmDeleteTemplate = (templateId) => {
        confirmDialog({
            group: 'headless',
            message: 'Are you sure you want to delete this template?',
            header: 'Delete Confirmation',
            icon: 'pi pi-exclamation-triangle',
            acceptClassName: 'p-button-danger',
            acceptLabel: 'Yes',
            rejectLabel: 'No',
            accept: () => handleDeleteConfirmed(templateId),
        });
    };

    // --- Actual delete handler after confirmation ---
    const handleDeleteConfirmed = async (id) => {
        try {
            await deleteTemplate.mutateAsync({ id: id }, {
                onSuccess: () => {
                    setLazyParams(prev => ({ ...prev }));
                },
                onError: () => {
                    toastRef.current && toastRef.current.show({
                        severity: 'error',
                        summary: 'Error',
                        detail: 'Failed to delete template',
                        life: 3000
                    });
                }
            });
        } catch (error) {
            toastRef.current && toastRef.current.show({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to delete template',
                life: 3000
            });
        }
    };

    // --- Edit Name Modal handler ---
    const openEditNameModal = (template) => {
        setEditNameModal({ open: true, template });
        setNewTemplateName(template.name);
    };
    const closeEditNameModal = () => {
        setEditNameModal({ open: false, template: null });
        setNewTemplateName('');
    };
    const handleEditNameSave = async () => {
        if (!editNameModal.template) return;
        try {
            await updateTemplateName.mutateAsync({ id: editNameModal.template.id, name: newTemplateName });
            setLazyParams(prev => ({ ...prev })); // Reload data
            toastRef.current && toastRef.current.show({
                severity: 'success',
                summary: 'Updated',
                detail: 'Template name updated successfully',
                life: 3000
            });
            closeEditNameModal();
        } catch (error) {
            // Check for duplicate name error
            const msg = error?.response?.data?.error;
            if (msg && msg.toLowerCase().includes('already exists')) {
                toastRef.current && toastRef.current.show({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'A design with this name already exists for your company.',
                    life: 4000
                });
            } else {
                toastRef.current && toastRef.current.show({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to update template name',
                    life: 3000
                });
            }
        }
    };

    // Mobile action menu component
    const MobileActionMenu = ({ template, isOpen, onClose }) => {
        if (!isOpen) return null;

        return createPortal(
            <div
                className="fixed inset-0 bg-black bg-opacity-60 z-[9999] flex items-center justify-center"
                style={{
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                    backdropFilter: 'blur(2px)'
                }}
                onClick={onClose}
            >
                <div
                    className="bg-white rounded-lg p-4 m-4 w-full max-w-sm relative shadow-2xl"
                    style={{
                        zIndex: 10000,
                        backgroundColor: '#ffffff',
                        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.5)',
                        border: '1px solid rgba(0, 0, 0, 0.1)'
                    }}
                    onClick={(e) => e.stopPropagation()}
                >
                    <div className="flex items-center mb-4 border-b pb-3">
                        <div className="w-10 h-10 rounded-full bg-purple-500 flex items-center justify-center mr-3">
                            <span className="text-white font-bold">{template.name?.charAt(0)?.toUpperCase()}</span>
                        </div>
                        <div>
                            <h3 className="font-semibold">{template.name}</h3>
                            <p className="text-sm text-gray-500">{template.card_type_name}</p>
                        </div>
                    </div>

                    <div className="space-y-2 bg-gray-50 p-2 rounded-lg">
                        {/* Edit Template */}
                        <button
                            className="w-full flex items-center p-3 text-left bg-white hover:bg-green-50 rounded-lg border border-gray-200"
                            onClick={() => {
                                navigate(`/manager/design-space/${template.id}`);
                                onClose();
                            }}
                        >
                            <FiEdit className="mr-3 text-green-500" size={18} />
                            <span>Edit Template</span>
                        </button>

                        {/* Edit Name */}
                        <button
                            className="w-full flex items-center p-3 text-left bg-white hover:bg-blue-50 rounded-lg border border-gray-200"
                            onClick={() => {
                                openEditNameModal(template);
                                onClose();
                            }}
                        >
                            <FiEdit className="mr-3 text-blue-500" size={18} />
                            <span>Edit Name</span>
                        </button>

                        {/* Delete Template */}
                        <button
                            className="w-full flex items-center p-3 text-left bg-white hover:bg-red-50 rounded-lg border border-gray-200 text-red-600"
                            onClick={() => {
                                confirmDeleteTemplate(template.id);
                                onClose();
                            }}
                        >
                            <TfiTrash className="mr-3" size={18} />
                            <span>Delete Template</span>
                        </button>
                    </div>

                    <button
                        className="w-full mt-4 p-2 bg-gray-200 hover:bg-gray-300 rounded-lg text-center font-medium transition-colors"
                        onClick={onClose}
                    >
                        Cancel
                    </button>
                </div>
            </div>,
            document.body
        );
    };

    // PropTypes validation for MobileActionMenu
    MobileActionMenu.propTypes = {
        template: PropTypes.shape({
            id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
            name: PropTypes.string,
            card_type_name: PropTypes.string,
        }),
        isOpen: PropTypes.bool,
        onClose: PropTypes.func,
    };

    // Mobile list view component
    const MobileListView = () => {
        if (loading) {
            return (
                <div className="space-y-2">
                    {[...Array(5)].map((_, index) => (
                        <div key={index} className="bg-[#23272F] border border-gray-800 rounded-lg p-4 shadow-lg animate-pulse">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center flex-1">
                                    <div className="w-12 h-12 bg-gray-300 rounded-lg mr-3"></div>
                                    <div className="flex-1">
                                        <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                                        <div className="h-3 bg-gray-300 rounded w-1/2"></div>
                                    </div>
                                </div>
                                <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
                            </div>
                        </div>
                    ))}
                </div>
            );
        }

        if (!data || data.length === 0) {
            return (
                <div className="text-center py-8">
                    <p className="text-gray-500">No templates found</p>
                </div>
            );
        }
        // ترتيب التصاميم من الأحدث للأقدم حسب created_at
        const sortedData = [...data].sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        return (
            <div className="space-y-2">
                {(sortedData || []).map((template) => (
                    <div key={template.id} className="border border-gray-900 rounded-2xl p-0 shadow-[0_8px_32px_0_rgba(0,0,0,0.65),0_1.5px_8px_0_rgba(0,0,0,0.25)] transition-all duration-300 hover:shadow-[0_16px_48px_0_rgba(0,0,0,0.80),0_3px_16px_0_rgba(0,0,0,0.35)] hover:-translate-y-1 group" style={{background: 'linear-gradient(135deg, #2d2d32 0%, #23272F 50%, #111114 100%)'}}>
                        <div className="flex items-center justify-between pl-3 pr-3 pt-4 pb-4">
                            <div className="flex items-center flex-1">
                                <div className="w-12 h-12 rounded-lg bg-purple-500 flex items-center justify-center mr-3">
                                    <span className="text-white font-bold text-lg">
                                        {template.name?.charAt(0)?.toUpperCase()}
                                    </span>
                                </div>
                                <div className="flex-1">
                                    <h3 className="text-2xl font-extrabold text-white tracking-wide drop-shadow-sm mb-1 truncate" style={{letterSpacing: '0.5px'}}>{template.name}</h3>
                                    <p className="text-sm text-gray-400 font-medium truncate" style={{letterSpacing: '0.2px'}}>{template.card_type_name}</p>
                                </div>
                            </div>
                            <button
                                className="p-2 hover:bg-gray-800 rounded-full"
                                onClick={() => setMobileActionMenuOpen(template.id)}
                            >
                                <HiDotsVertical className="text-gray-300" size={20} />
                            </button>
                        </div>
                    </div>
                ))}

                {/* Mobile Action Menu */}
                {mobileActionMenuOpen && (
                    <MobileActionMenu
                        template={sortedData.find(t => t.id === mobileActionMenuOpen)}
                        isOpen={!!mobileActionMenuOpen}
                        onClose={() => setMobileActionMenuOpen(null)}
                    />
                )}
            </div>
        );
    };

    // PropTypes validation for MobileActionMenu
    MobileListView.propTypes = {
        data: PropTypes.arrayOf(PropTypes.shape({
            id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
            name: PropTypes.string,
            card_type_name: PropTypes.string,
        })),
        loading: PropTypes.bool,
        setMobileActionMenuOpen: PropTypes.func,
    };

    // --- Card View for Desktop ---
    const CardListView = () => {
        if (loading) {
            return (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {[...Array(lazyParams?.rows || 5)].map((_, idx) => (
                        <div key={idx} className="bg-[#23272F] border rounded-xl p-6 shadow-lg animate-pulse h-[340px] flex flex-col justify-between" />
                    ))}
                </div>
            );
        }
        if (!data || data.length === 0) {
            return (
                <div className="text-center py-16 text-gray-500 text-lg font-medium">No templates found</div>
            );
        }
        // ترتيب التصاميم من الأحدث للأقدم حسب created_at
        const sortedData = [...data].sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        return (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8">
                {(sortedData || []).map((template) => (
                    <div key={template.id} className="border border-gray-900 rounded-2xl shadow-[0_8px_32px_0_rgba(0,0,0,0.65),0_1.5px_8px_0_rgba(0,0,0,0.25)] hover:shadow-[0_16px_48px_0_rgba(0,0,0,0.80),0_3px_16px_0_rgba(0,0,0,0.35)] hover:-translate-y-2 transition-all duration-300 flex flex-col overflow-hidden group" style={{background: 'linear-gradient(135deg, #2d2d32 0%, #23272F 50%, #111114 100%)'}}>
                        <div className="relative group cursor-pointer px-4 pt-4" onClick={() => {
                            setSelectedTemplate(template.template);
                            setTemplateModalVisible(true);
                            console.log('Open modal for template:', template.name, template.template);
                        }}>
                            {/* Responsive aspect-ratio wrapper */}
                            <div
                                className="relative w-full bg-gradient-to-br from-gray-50 to-gray-200 rounded-xl border border-gray-100 shadow-inner"
                                style={{
                                    aspectRatio: `${CARD_WIDTH} / ${CARD_HEIGHT}`,
                                    maxWidth: `${CARD_CONTAINER_MAX_WIDTH}px`,
                                    maxHeight: `${CARD_CONTAINER_MAX_HEIGHT}px`,
                                    margin: '0 auto',
                                    padding: '12px',
                                    overflow: 'hidden',
                                    position: 'relative',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    height: '100%',
                                    width: '100%',
                                }}
                            >
                                <div
                                    style={{
                                        width: '100%',
                                        height: '100%',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'flex-end',
                                        paddingRight: '60px',
                                         paddingBottom: '123px',
                                    }}
                                >
                                    <div
                                        style={{
                                            width: `${CARD_WIDTH}px`,
                                            height: `${CARD_HEIGHT}px`,
                                            
                                            transform: 'scale(0.66)', 
                                            transformOrigin: 'center center',
                                            pointerEvents: 'none',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                        }}
                                    >
                                        {template.template ? (
                                            <div style={{ width: '100%', height: '100%' }}>{parse(hideBraces(template.template))}</div>
                                        ) : (
                                            <div className="text-gray-400 text-center">No design</div>
                                        )}
                                    </div>
                                </div>
                                {/* Overlay covers the entire preview area, icon is centered absolutely */}
                                <div
                                    className="absolute top-0 left-0 w-full h-full bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 rounded-xl"
                                    style={{ pointerEvents: 'none' }}
                                >
                                    <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                                        <div className="text-white opacity-0 group-hover:opacity-100 transform scale-90 group-hover:scale-100 transition-all duration-300">
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="flex-1 flex flex-col justify-between pt-5 pb-5 pl-3 pr-3">
                            <div>
                                <h3 className="text-2xl font-extrabold text-white tracking-wide drop-shadow-sm mb-1 truncate" style={{letterSpacing: '0.5px'}}>{template.name}</h3>
                            </div>
                            <p className="text-sm text-gray-400 font-medium mt-1 mb-2 truncate" style={{letterSpacing: '0.2px'}}>{template.card_type_name}</p>
                            <div className="flex gap-2 mt-4">
                                <button
                                    className="flex-1 flex items-center justify-center gap-2 px-3 py-2 rounded-xl bg-[#18191C]/70 border border-green-500 text-green-400 font-bold hover:bg-green-900/30 hover:shadow-[0_0_16px_2px_#22c55e] hover:text-green-300 hover:border-green-400 hover:scale-[1.04] transition-all duration-200 group"
                                    onClick={() => navigate(`/manager/design-space/${template.id}`)}
                                >
                                    <FiEdit size={16} className="text-green-400 group-hover:text-green-300 drop-shadow-[0_0_4px_#22c55e]" /> Edit
                                </button>
                                <button
                                    className="flex-1 flex items-center justify-center gap-2 px-3 py-2 rounded-xl bg-[#18191C]/70 border border-blue-500 text-blue-400 font-bold hover:bg-blue-900/30 hover:shadow-[0_0_16px_2px_#3b82f6] hover:text-blue-300 hover:border-blue-400 hover:scale-[1.04] transition-all duration-200 group"
                                    onClick={() => openEditNameModal(template)}
                                >
                                    <FiEdit size={16} className="text-blue-400 group-hover:text-blue-300 drop-shadow-[0_0_4px_#3b82f6]" /> Name
                                </button>
                                <button
                                    className="flex-1 flex items-center justify-center gap-2 px-3 py-2 rounded-xl bg-[#18191C]/70 border border-red-500 text-red-400 font-bold hover:bg-red-900/30 hover:shadow-[0_0_16px_2px_#ef4444] hover:text-red-300 hover:border-red-400 hover:scale-[1.04] transition-all duration-200 group"
                                    onClick={() => confirmDeleteTemplate(template.id)}
                                >
                                    <TfiTrash size={16} className="text-red-400 group-hover:text-red-300 drop-shadow-[0_0_4px_#ef4444]" /> Delete
                                </button>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        );
    };

    const buildUrlWithFilters = (searchQuery, cardTypeFilter) => {
        let filterQuery = '';
        if (searchQuery.trim()) {
            filterQuery += `filter[name]=${encodeURIComponent(searchQuery.trim())}`;
        }
        if (cardTypeFilter !== 'all') {
            if (filterQuery.length > 0) filterQuery += '&';
            filterQuery += `filter[card_type_id]=${encodeURIComponent(cardTypeFilter)}`;
        }
        if (filterQuery.length > 0) filterQuery += '&';
        filterQuery += 'sort=-created_at';

        let url = 'get-designs-list';
        if (filterQuery.length > 0) {
            url += `?${filterQuery}`;
        }
        return url;
    };

    return (

        <Container>
            <Toast ref={toastRef} />
            <ConfirmDialog />
            <Dialog
                visible={templateModalVisible}
                onHide={() => {
                    setTemplateModalVisible(false);
                    setTemplateZoomLevel(1.0);
                }}
                header={
                    <div className="flex justify-between items-center w-full">
                        <span className="mr-1">Template Preview</span>
                        <div className="flex items-center gap-2 mr-8">
                            <button
                                className="p-2 rounded-full hover:bg-gray-200"
                                onClick={() => setTemplateZoomLevel(prev => Math.max(0.5, prev - 0.1))}
                                title="Zoom Out"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </button>
                            <span className="mx-2 text-gray-600">Zoom: {Math.round(templateZoomLevel * 100)}%</span>
                            <button
                                className="p-2 rounded-full hover:bg-gray-200"
                                onClick={() => setTemplateZoomLevel(prev => Math.min(2.0, prev + 0.1))}
                                title="Zoom In"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                                </svg>
                            </button>
                        </div>
                    </div>
                }
                style={{ width: '95vw', maxWidth: '1200px' }}
                modal
                className="template-preview-dialog"
                contentClassName="p-0 overflow-hidden"
                contentStyle={{ maxHeight: '90vh', overflow: 'auto', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
                draggable={false}
                resizable={false}
                maximizable={false}
                closeOnEscape
                closeOnBackdropClick
            >
                {!fontsLoaded ? (
                    <div style={{textAlign: 'center', padding: '40px', fontSize: '18px'}}>Loading fonts...</div>
                ) : (
                    <div
                        style={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: '100%',
                            height: '100%',
                            minHeight: '60vh',
                            minWidth: '60vw',
                            overflow: 'auto',
                            background: '#f3f4f6',
                            borderRadius: '12px',
                            padding: '24px'
                        }}
                    >
                        <div
                            style={{
                                boxShadow: '0 4px 24px rgba(0,0,0,0.10)',
                                background: '#fff',
                                borderRadius: '12px',
                                padding: '0',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                maxWidth: '100%',
                                maxHeight: '80vh',
                                overflow: 'auto',
                                transform: `scale(${templateZoomLevel})`,
                                transformOrigin: 'center center',
                                transition: 'transform 0.2s'
                            }}
                        >
                            {selectedTemplate && parse(hideBraces(selectedTemplate))}
                        </div>
                    </div>
                )}
            </Dialog>

            <div className="flex justify-between items-center mb-4 mb-24">
                <div className="flex items-center w-full">
                  
                    <div className="relative mr-auto">
                        <span className="absolute inset-y-0 left-0 pl-3 flex items-center">
                            <FaSearch className="text-gray-400" size={18} />
                        </span>
                        <input
                            type="text"
                            placeholder="Search templates..."
                            className="pl-10 pr-4 py-2 rounded-lg bg-gray-800 text-white border border-gray-700 focus:outline-none focus:border-blue-500 w-80"
                            style={{ width: '600px' , marginRight: '20px'}}
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                        />
                    </div>
                    {/* الفلتر وزر الإضافة كما كانوا */}
                    <div className="flex items-center gap-2">
                        <Dropdown
                            value={cardTypeFilter}
                            options={cardTypeOptions}
                            onChange={(e) => setCardTypeFilter(e.value)}
                            placeholder="Filter by type"
                            className="w-40"
                        />
                        <Button
                            label="Add New Template"
                            icon="pi pi-plus"
                            onClick={() => navigate('/manager/design-space')}
                            className={`main-btn ${isMobile ? 'text-sm' : 'text-md'} shadow-md ${isMobile ? 'mr-2' : 'mr-[20px]'}`}
                        />
                    </div>
                </div>
            </div>

            {isMobile ? (
                <MobileListView />
            ) : (
                <CardListView />
            )}
        </Container>
    );
}

export default TemplatesDataTable;