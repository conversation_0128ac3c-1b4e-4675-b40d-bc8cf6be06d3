import React, { memo, useMemo } from 'react';
import { motion } from 'framer-motion';
import {
  FaBluetooth,
  FaWifi,
  FaQrcode,
  FaCreditCard,
  FaMobileAlt
} from 'react-icons/fa';
import { LuNfc } from "react-icons/lu";

const CardPreview = memo(({ card, isSelected = false, onClick, className = '' }) => {
  // Extract dimensions from card settings
  const width = card?.setting?.width || 350;
  const height = card?.setting?.height || 200;
  
  // Calculate aspect ratio and scale for consistent display
  const aspectRatio = width / height;

  // Responsive max dimensions based on screen size
  const isMobile = window.innerWidth < 768;
  const isTablet = window.innerWidth >= 768 && window.innerWidth < 1024;

  let maxDisplayWidth, maxDisplayHeight;
  if (isMobile) {
    maxDisplayWidth = 200;
    maxDisplayHeight = 140;
  } else if (isTablet) {
    maxDisplayWidth = 220;
    maxDisplayHeight = 150;
  } else {
    maxDisplayWidth = 240;
    maxDisplayHeight = 160;
  }
  
  // Calculate display dimensions while maintaining aspect ratio
  let displayWidth, displayHeight;
  
  if (aspectRatio > maxDisplayWidth / maxDisplayHeight) {
    // Width is the limiting factor
    displayWidth = maxDisplayWidth;
    displayHeight = maxDisplayWidth / aspectRatio;
  } else {
    // Height is the limiting factor
    displayHeight = maxDisplayHeight;
    displayWidth = maxDisplayHeight * aspectRatio;
  }

  // Memoize expensive calculations
  const cardGradient = useMemo(() => {
    const type = card?.type_of_connection?.toLowerCase() || 'default';
    const gradients = {
      bluetooth: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', 
      nfc: 'linear-gradient(135deg, #100021 0%, #4f0084 100%)', //'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
      qr: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', //'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
      wifi: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
      default: 'linear-gradient(135deg, #00c3ac 0%, #02aa96 100%)'
    };
    return gradients[type] || gradients.default;
  }, [card?.type_of_connection]);

  const connectionIcon = useMemo(() => {
    const iconType = card?.type_of_connection?.toLowerCase() || 'default';
    const iconProps = { size: 32, color: 'white' };

    const icons = {
      bluetooth: <FaBluetooth {...iconProps} />,
      nfc: <LuNfc {...iconProps} />,
      qr: <FaQrcode {...iconProps} />,
      wifi: <FaWifi {...iconProps} />,
      default: <FaCreditCard {...iconProps} />
    };
    return icons[iconType] || icons.default;
  }, [card?.type_of_connection]);

  return (
    <motion.div
      className={`card-preview-container ${className}`}
      onClick={onClick}
      whileHover={{
        scale: 1.02,
        y: -2,
        transition: { duration: 0.2, ease: "easeOut" }
      }}
      whileTap={{
        scale: 0.98,
        transition: { duration: 0.1, ease: "easeOut" }
      }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      role="button"
      tabIndex={0}
      aria-label={`Card type: ${card?.name || 'Unnamed'}, ${width}x${height} pixels`}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onClick && onClick();
        }
      }}
      style={{
        cursor: 'pointer',
        padding: '20px',
        borderRadius: '16px',
        background: 'white',
        boxShadow: isSelected
          ? '0 8px 32px rgba(0, 0, 0, 0.12), 0 0 0 2px #00c3ac'
          : '0 4px 16px rgba(0, 0, 0, 0.08)',
        border: isSelected ? '2px solid #00c3ac' : '2px solid transparent',
        transition: 'all 0.3s ease',
        minHeight: '280px',
        maxHeight: '320px',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}
    >
      {/* Card Visual Preview */}
      <div
        className="card-visual"
        style={{
          width: `${displayWidth}px`,
          height: `${displayHeight}px`,
          background: cardGradient,
          borderRadius: '12px',
          position: 'relative',
          overflow: 'hidden',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginBottom: '16px'
        }}
      >
        {/* Card content overlay */}
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(255, 255, 255, 0.1)',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            textAlign: 'center',
            padding: '12px'
          }}
        >
          {/* Connection type icon */}
          <div style={{ marginBottom: '8px' }}>
            {connectionIcon}
          </div>

          {/* Dimensions display only */}
          <div
            style={{
              fontSize: displayWidth > 200 ? '11px' : '9px',
              opacity: 0.9,
              textShadow: '0 1px 2px rgba(0, 0, 0, 0.3)',
              fontWeight: '500'
            }}
          >
            {width} × {height} px
          </div>
        </div>

        {/* Decorative elements */}
        <div
          style={{
            position: 'absolute',
            top: '8px',
            right: '8px',
            width: '20px',
            height: '20px',
            borderRadius: '50%',
            background: 'rgba(255, 255, 255, 0.2)',
            border: '1px solid rgba(255, 255, 255, 0.3)'
          }}
        />
        <div
          style={{
            position: 'absolute',
            bottom: '8px',
            left: '8px',
            width: '16px',
            height: '16px',
            borderRadius: '50%',
            background: 'rgba(255, 255, 255, 0.15)',
            border: '1px solid rgba(255, 255, 255, 0.2)'
          }}
        />
      </div>

      {/* Card Information */}
      <div style={{ width: '100%', textAlign: 'center' }}>
        {/* Card name only */}
        <h3
          style={{
            fontSize: '16px',
            fontWeight: '600',
            color: '#1f2937',
            margin: '0',
            lineHeight: '1.3'
          }}
        >
          {card?.name || 'Unnamed Card'}
        </h3>
      </div>
    </motion.div>
  );
});

CardPreview.displayName = 'CardPreview';

export default CardPreview;
