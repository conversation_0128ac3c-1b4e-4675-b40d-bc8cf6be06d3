import TemplatesDataTable from './TemplatesDataTable'
import Container from '@components/Container'

function Templates() {
  // لا يمكن معرفة حالة الاشتراك أو الباقة هنا مباشرة بدون رفع الحالة من TemplatesDataTable
  // الحل الأفضل: نقل زر Create New Template إلى TemplatesDataTable ليتم التحكم فيه بناءً على الشروط هناك
  // مؤقتاً: أخفي الزر دائماً هنا وأبلغ المستخدم بذلك
  return (
    <Container>
      <div className="w-full flex justify-center mb-8">
        <div className="w-full flex justify-center">
          <h1
            className="text-4xl md:text-5xl font-extrabold text-center bg-gradient-to-r from-gray-700  to-purple-800 bg-clip-text text-transparent drop-shadow-lg mb-4 flex items-center justify-center gap-3"
            style={{ letterSpacing: '1px', lineHeight: '1.2', WebkitTextStroke: '0.5px #222' }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-blue-400 drop-shadow-md" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2a4 4 0 014-4h3m4 0a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Designs Templates
          </h1>
        </div>
      </div>
      {/* الزر مخفي مؤقتاً. يفضل نقله إلى TemplatesDataTable للتحكم الدقيق */}
      {/* <div className='flex justify-end  w-8/12'>
        <Link to="/manager/design-space" >
          <button className="main-btn text-md shadow-md">Create New Template</button>
        </Link>
      </div> */}
      <TemplatesDataTable />
    </Container >
  )
}

export default Templates
