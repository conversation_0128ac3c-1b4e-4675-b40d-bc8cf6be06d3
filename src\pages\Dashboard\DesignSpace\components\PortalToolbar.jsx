import { createPortal } from "react-dom";

export default function PortalToolbar({ rect, designSpaceRect, isMobile, children }) {
  if (!rect || !designSpaceRect) return null;

  // إعدادات الشريط
  const edgeThreshold = 50;
  const toolbarWidth = isMobile ? 180 : 140;
  const toolbarHeight = isMobile ? 44 : 32;
  const toolbarDistance = isMobile ? 60 : 40;
  const offsetFromEdge = isMobile ? 40 : 24;

  // حساب المسافات من الحواف
  const elementTop = rect.top - designSpaceRect.top;
  const elementRight = designSpaceRect.right - rect.right;
  const elementLeft = rect.left - designSpaceRect.left;
  const elementBottom = designSpaceRect.bottom - rect.bottom;
  const elementWidth = rect.width;
  const elementHeight = rect.height;

  // منطق الحواف
  const isNearTop = elementTop < edgeThreshold;
  const isNearRight = elementRight < edgeThreshold;
  const isNearLeft = elementLeft < edgeThreshold;
  const isNearBottom = elementBottom < edgeThreshold;

  // الوضع الافتراضي: فوق العنصر
  let top = rect.top - toolbarDistance;
  let left = rect.left + elementWidth / 2;
  let transform = "translateX(-50%)";

  // إذا كان قريب من الأعلى جداً، ضع الشريط أسفل العنصر
  if (isNearTop && !isNearRight && !isNearLeft) {
    top = rect.bottom + toolbarDistance * 1.0; // مسافة متوسطة للأسفل
    left = rect.left + elementWidth / 2;
    transform = "translateX(-40%)";
  } else if (isNearBottom && !isNearRight && !isNearLeft) {
    top = rect.top - toolbarDistance;
    left = rect.left + elementWidth / 2;
    transform = "translateX(-40%)";
  } else if (isNearRight && (isNearTop || isNearBottom)) {
    // إذا كان قريب من الزاوية العليا أو السفلى اليمنى
    if (isNearTop) {
      top = rect.bottom + toolbarDistance * 0.8; // مسافة متوسطة للأسفل
      left = rect.left + elementWidth / 2;
      transform = "translateX(-60%)";
    } else if (isNearBottom) {
      top = rect.top - toolbarDistance;
      left = rect.left + elementWidth / 2;
      transform = "translateX(-60%)";
    }
  } else if (isNearLeft && (isNearTop || isNearBottom)) {
    // إذا كان قريب من الزاوية العليا أو السفلى اليسرى
    if (isNearTop) {
      top = rect.bottom + toolbarDistance * 1.2; // مسافة متوسطة للأسفل
      left = rect.left + elementWidth / 2;
      transform = "translateX(-40%)";
    } else if (isNearBottom) {
      top = rect.top - toolbarDistance;
      left = rect.left + elementWidth / 2;
      transform = "translateX(-40%)";
    }
  } else if (isNearRight && !isNearTop && !isNearBottom) {
    // إذا كان قريب من اليمين فقط، اجعل الشريط فوق العنصر مع الإزاحة
    top = rect.top - toolbarDistance;
    left = rect.left + elementWidth / 2;
    transform = "translateX(-60%)";
  } else if (isNearLeft && !isNearTop && !isNearBottom) {
    // إذا كان قريب من اليسار فقط، اجعل الشريط فوق العنصر مع الإزاحة
    top = rect.top - toolbarDistance;
    left = rect.left + elementWidth / 2;
    transform = "translateX(-40%)";
  }

  // منع خروج الشريط من الشاشة (يمين/يسار)
  // الإزاحة الجانبية ثابتة كنسبة من عرض منطقة التصميم على جميع الشاشات
  const sideOffset = (designSpaceRect.right - designSpaceRect.left) * 0.04; // 4% من عرض منطقة التصميم
  left = Math.max(designSpaceRect.left + toolbarWidth / 2 + sideOffset, Math.min(left, designSpaceRect.right - toolbarWidth / 2 - sideOffset));
  // منع خروج الشريط من الأعلى
  top = Math.max(designSpaceRect.top + 8, top - 20); // رفع الشريط للأعلى 20px

  // بدلاً من createPortal إلى document.body، سنستخدم أقرب parent (designSpace)
  // نحتاج تمرير ref أو id لمنطقة التصميم من الأعلى
  // سنستخدم position: absolute بدلاً من fixed

  // ابحث عن عنصر منطقة التصميم
  const designSpaceEl = document.querySelector('.design-space');
  if (!designSpaceEl) return null;

  // احسب top/left بالنسبة لمنطقة التصميم
  const parentRect = designSpaceEl.getBoundingClientRect();
  const absTop = top - parentRect.top + designSpaceEl.scrollTop;
  const absLeft = left - parentRect.left + designSpaceEl.scrollLeft;

  return createPortal(
    <div
      style={{
        position: "absolute",
        top: absTop,
        left: absLeft,
        transform,
        zIndex: 99999,
        pointerEvents: 'auto',
        transition: 'top 0.18s, left 0.18s',
      }}
    >
      {children}
    </div>,
    designSpaceEl
  );
} 