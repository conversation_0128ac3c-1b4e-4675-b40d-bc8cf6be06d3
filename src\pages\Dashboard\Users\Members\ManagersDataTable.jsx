import { useState, useRef, useEffect } from "react";
import { <PERSON>a<PERSON><PERSON>, <PERSON>a<PERSON><PERSON>ch, <PERSON>a<PERSON>ser, FaB<PERSON>ing, FaEnvelope, FaPhone } from "react-icons/fa";
import { Dialog } from "primereact/dialog";
import { Dropdown } from "primereact/dropdown";
import { InputNumber } from "primereact/inputnumber";
import { Button } from "primereact/button";
import { Toast } from "primereact/toast";
import axios from "axios";
import { Tooltip } from "primereact/tooltip";
import CreateCardToManagerForm from "../../Backages/CreateCardToManagerForm";
import ManagerDetailModal from "./ManagerDetailModal";

// Add these imports for the animated SVGs
import { motion } from "framer-motion";
import { HiOutlineCreditCard } from "react-icons/hi";
import { BsBank2 } from "react-icons/bs";

// Import the context hook
import { useDataTableContext } from "../../../../contexts/UsersDataTableContext";
import { useGlobalContext } from "../../../../contexts/GlobalContext";
import { useLayout } from "../../../../contexts/LayoutContext";

import AssignGroupDialog from "../Groups/AssignGroupDialog";
import AddMemberDialog from "./AddMemberDialog";
import { TfiTrash } from "react-icons/tfi";
import { FiEdit } from "react-icons/fi";
import { confirmDialog, ConfirmDialog } from 'primereact/confirmdialog';
import axiosInstance from "../../../../config/Axios";
import PropTypes from 'prop-types';



// Define managers table config specifically for this component
const managersTableConfig = {
  url: "datatable/managers",
  filters: {
    'name': { value: '', matchMode: 'contains' },
    'email': { value: '', matchMode: 'contains' },
    'phone': { value: '', matchMode: 'contains' },
    'company_name': { value: '', matchMode: 'contains' },
    'status': { value: '', matchMode: 'contains' },
  }
};

// 1. Add import for react-beautiful-dnd at the top



function Header({ searchQuery, handleSearchChange, isMobile, createMember }) {
  const userRole = localStorage.getItem("user_role");
  return (
    <div className="w-full">
      <div className={`w-full mb-4 ${isMobile ? 'flex flex-col gap-3' : 'flex items-center'}`}>
        {/* Search Bar - Always centered */}
        <div className={`relative ${isMobile ? 'w-full' : 'flex-grow max-w-[600px] mx-auto'}`}>
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FaSearch className="h-4 w-4 text-gray-400" aria-hidden="true" />
          </div>
          <input
            type="text"
            placeholder="Search by manager name..."
            className="w-full pl-10 pr-4 py-2 border rounded-md shadow-md
                      focus:outline-none focus:ring-2 focus:ring-blue-300
                      focus:border-blue-300 transition-all duration-200"
            value={searchQuery}
            onChange={handleSearchChange}
          />
        </div>
        {/* Add Manager button - Pushed to right on desktop */}
        <div className={`${isMobile ? 'w-full' : 'flex-shrink-0 ml-4'}`}>
          {userRole !== "user" && (
            <button
              className={`main-btn text-md shadow-md ${isMobile ? 'w-full' : ''}`}
              onClick={() => createMember()}
            >
              Add Manager
            </button>
          )}
        </div>
      </div>
    </div>
  );
}

Header.propTypes = {
  searchQuery: PropTypes.string.isRequired,
  handleSearchChange: PropTypes.func.isRequired,
  isMobile: PropTypes.bool.isRequired,
  createMember: PropTypes.func.isRequired,
};

function ManagersDataTable() {
  const toastRef = useRef(null);
  const [actionType, setActionType] = useState("");
  const [selectedMember, setSelectedMember] = useState({});
  const [giftDialogVisible, setGiftDialogVisible] = useState(false);
  const [formData, setFormData] = useState({ user_id: null, package_id: null, duration: 12, payment_method: "gift", total_price: 0 });
  const [availablePackages, setAvailablePackages] = useState([]);
  const [selectedPackage, setSelectedPackage] = useState(null);
  const [giftLoading, setGiftLoading] = useState(false);
  const [animationKey, setAnimationKey] = useState(0);
  const [managerDetailModalVisible, setManagerDetailModalVisible] = useState(false);
  const [selectedManagerForModal, setSelectedManagerForModal] = useState(null);
  const [managerActivePackages, setManagerActivePackages] = useState(null);
  const [managerPackagesError, setManagerPackagesError] = useState(null);
  const [loadingManagerPackages, setLoadingManagerPackages] = useState(false);
  const [managerPackageHistory, setManagerPackageHistory] = useState([]);
  const [managerPackageHistoryError, setManagerPackageHistoryError] = useState(null);
  const [loadingManagerPackageHistory, setLoadingManagerPackageHistory] = useState(false);
  const [managerCardsUsageOverride, setManagerCardsUsageOverride] = useState({});
  // عداد render
  const renderCount = useRef(0);
  renderCount.current += 1;
  console.log("ManagersDataTable render count:", renderCount.current);

  const {
    setLazyManagersParams,
    loading: tableLoading,
    data,
    totalRecords,
    lazyManagersParams,
  } = useDataTableContext();
  const { dialogHandler, openDialog } = useGlobalContext();
  const { isMobile } = useLayout();

  // Search state
  const [searchQuery, setSearchQuery] = useState("");
  const [allManagers, setAllManagers] = useState([]);

  console.log("data from context", data);
  console.log("searchQuery", searchQuery);

  useEffect(() => {
      fetchAllManagers();
  }, []);

  const fetchAllManagers = async () => {
    try {
      const token = localStorage.getItem("token");
      const response = await axios.get(
        `${import.meta.env.VITE_BACKEND_URL}/datatable/managers?all=1`,
        { headers: { Authorization: `Bearer ${token}` } }
      );
      setAllManagers(response.data.data || []);
      console.log("allManagers count:", (response.data.data || []).length);
    } catch (error) {
      console.error("Error fetching all managers:", error);
    }
  };

  const handleSearchChange = (e) => setSearchQuery(e.target.value);

  // عند البحث أو تغيير الصفحة، أرسل الفلتر للـ backend:
  useEffect(() => {
    const filters = searchQuery.trim()
      ? { name: { value: searchQuery.trim(), matchMode: 'contains' } }
      : {};
    setLazyManagersParams(prev => ({
      ...prev,
      filters,
      first: 0,
      page: 0,
    }));
  }, [searchQuery, setLazyManagersParams]);

  // عند تغيير الصفحة:
  const goToNextPage = () => {
    if (lazyManagersParams.first + lazyManagersParams.rows < totalRecords) {
      setLazyManagersParams(prev => ({
        ...prev,
        first: prev.first + prev.rows,
        page: prev.page + 1,
        // أعد إرسال نفس الفلاتر
        filters: prev.filters || {},
      }));
    }
  };
  const goToPrevPage = () => {
    if (lazyManagersParams.first > 0) {
      setLazyManagersParams(prev => ({
        ...prev,
        first: prev.first - prev.rows,
        page: prev.page - 1,
        filters: prev.filters || {},
      }));
    }
  };
  const handleRowsPerPageChange = (e) => {
    setLazyManagersParams(prev => ({
      ...prev,
      rows: parseInt(e.target.value, 10),
      first: 0,
      page: 0,
      filters: prev.filters || {},
    }));
  };

  const createMember = () => {
    setActionType("create");
    setSelectedMember({});
    dialogHandler("addMember");
  };



  const editMember = (data) => {
    setActionType("update");
    const updatedData = { ...data };
    delete updatedData.role;
    delete updatedData.group_permission;
    setSelectedMember(updatedData);
    dialogHandler("addMember");
  };

  // Delete handler with modal
  const handleDeleteClick = (rowData) => {
    confirmDialog({
      message: 'Are you sure you want to delete this manager?',
      header: 'Confirmation',
      icon: 'pi pi-exclamation-triangle',
      acceptClassName: 'p-button-danger',
      acceptLabel: 'Yes',
      rejectLabel: 'No',
      accept: () => deleteManager(rowData),
    });
  };

  // Delete logic: remove from UI and show toast
  const deleteManager = async () => {
    try {

      toastRef.current?.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Manager deleted successfully (local only)',
        life: 3000
      });
    } catch (error) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to delete manager',
        life: 3000
      });
    }
  };

  const fetchAvailablePackages = async () => {
    try {
      const token = localStorage.getItem("token");
      const response = await axios.get(
        `${import.meta.env.VITE_BACKEND_URL}/packages/original_packages`,
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );
      setAvailablePackages(response.data || []);
    } catch (error) {
      console.error("Error fetching packages:", error);
      toastRef.current.show({
        severity: "error",
        summary: "Error",
        detail: "Failed to fetch available packages",
        life: 3000
      });
    }
  };

  // Submit gift form
  const handleGiftSubmit = async () => {
    try {
      setGiftLoading(true);
      const token = localStorage.getItem("token");

      // For bank_transfer, we'll use the manually entered total_price directly
      // No need to recalculate it here

      await axios.post(
        `${import.meta.env.VITE_BACKEND_URL}/admin/assign-package-to-user`,
        {
          user_id: formData.user_id,
          package_id: formData.package_id,
          duration: formData.duration,
          payment_method: formData.payment_method,
          total_price: formData.payment_method === "bank_transfer" ? formData.total_price : 0
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json"
          }
        }
      );

      toastRef.current.show({
        severity: "success",
        summary: "Success",
        detail: "Package has been successfully assigned to the user",
        life: 3000
      });

      setGiftDialogVisible(false);
      // Update data - use managersTableConfig instead of usersTableConfig
      setLazyManagersParams((prev) => ({ ...prev, ...managersTableConfig }));
    } catch (error) {
      console.error("Error assigning package:", error);
      toastRef.current.show({
        severity: "error",
        summary: "Error",
        detail: error.response?.data?.message || "An error occurred while assigning the package",
        life: 3000
      });
    } finally {
      setGiftLoading(false);
    }
  };

  const openGiftDialog = (rowData) => {
    setFormData({
      ...formData,
      user_id: rowData.id,
      package_id: null,
      duration: 12,
      payment_method: "gift"
    });
    fetchAvailablePackages();
    setGiftDialogVisible(true);
  };



  // Manager card click handler
  const handleManagerCardClick = async (manager) => {
    if (managerDetailModalVisible) {
      return;
    }
    setSelectedManagerForModal(manager);
    setManagerDetailModalVisible(true);
    setManagerActivePackages(null);
    setManagerPackagesError(null);
    setLoadingManagerPackages(true);
    setManagerPackageHistory([]);
    setManagerPackageHistoryError(null);
    setLoadingManagerPackageHistory(true);
    try {
      const token = localStorage.getItem("token");
      // Fetch active packages (existing logic)
      const response = await axios.get(
        `${import.meta.env.VITE_BACKEND_URL}/packages/active-with-cards?user_id=${manager.id}`,
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );
      setManagerActivePackages(response.data);
      if (response.data.active_packages && response.data.active_packages.length > 0) {
        const active = response.data.active_packages[0];
        setManagerCardsUsageOverride(prev => ({
          ...prev,
          [manager.id]: {
            cards_count: active.cards_count,
            card_limit: active.card_limit
          }
        }));
      }
      // Fetch full package history
      const historyRes = await axiosInstance.get(`/packages/${manager.id}/packages_history`);
      setManagerPackageHistory(historyRes.data.history_packages || []);
    } catch (error) {
      setManagerPackagesError(error?.response?.data?.message || "Failed to fetch active packages");
      setManagerPackageHistoryError(error?.response?.data?.message || "Failed to fetch package history");
    } finally {
      setLoadingManagerPackages(false);
      setLoadingManagerPackageHistory(false);
    }
  };

  // Manager card component
  const ManagerCard = ({ manager }) => {
    const lastPackage = manager.packages && manager.packages.length > 0
      ? manager.packages[manager.packages.length - 1]
      : null;

    // Get active package for progress bar
    const activePackage = manager.packages && manager.packages.length > 0
      ? manager.packages.find(pkg => pkg.status === 'active')
      : null;
    // استخدم override إذا توفر
    const usageOverride = managerCardsUsageOverride[manager.id];
    const cardLimit = usageOverride?.card_limit ?? activePackage?.card_limit ?? 0;
    const cardsCount = usageOverride?.cards_count ?? activePackage?.cards_count ?? 0;

    // متغير لحساب نسبة الاشتراك
    let subscriptionPercent = 0;
    if (activePackage && activePackage.purchased_at && activePackage.expiry_date) {
      const now = new Date();
      const start = new Date(activePackage.purchased_at);
      const end = new Date(activePackage.expiry_date);
      const total = end - start;
      const used = now - start;
      subscriptionPercent = total > 0 ? Math.min(100, Math.max(0, Math.round((used / total) * 100))) : 0;
    }
    const cardsBarRef = useRef(null);
    const subscriptionBarRef = useRef(null);
    const [showCardsPercent, setShowCardsPercent] = useState(false);
    const [showSubPercent, setShowSubPercent] = useState(false);

    const [cardsPercentPos, setCardsPercentPos] = useState({ x: 0, y: 0 });
    const [subPercentPos, setSubPercentPos] = useState({ x: 0, y: 0 });

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        whileHover={{ y: -5, scale: 1.02 }}
        transition={{ duration: 0.3 }}
        className="bg-white rounded-xl shadow-lg hover:shadow-xl border border-gray-200 overflow-hidden cursor-pointer group flex flex-col h-full"
        onClick={() => handleManagerCardClick(manager)}
      >
        {/* Card Header */}
        <div className="p-6 pb-4">
          <div className="flex items-center space-x-4">
            {/* Profile Image */}
            <div className="relative">
              <img
                src={manager.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(manager.name)}&background=00c3ac&color=fff&size=64`}
                alt={manager.name}
                className="w-16 h-16 rounded-full object-cover border-2 border-gray-200 group-hover:border-[#00c3ac] transition-colors duration-300"
              />
            </div>
            {/* Manager Info */}
            <div className="flex-1 min-w-0">
              <h3 className="text-lg font-bold text-gray-900 truncate group-hover:text-[#00c3ac] transition-colors duration-300">
                {manager.name}
              </h3>
              <p className="text-sm text-gray-600 truncate">
                <FaBuilding className="inline mr-1" />
                {manager.company_name || 'No Company'}
              </p>
              <div style={{ display: "flex", alignItems: "center", marginTop: "0.25rem" }}>
                <span
                  style={{
                    display: "inline-flex",
                    alignItems: "center",
                    padding: "0.25rem 0.5rem",
                    borderRadius: "9999px",
                    fontSize: "0.75rem",
                    fontWeight: 500,
                    color: "#fff",
                    backgroundColor: lastPackage && lastPackage.status === "active" ? "#22C55E" : lastPackage ? "#EF4444" : "#6B7280",
                  }}
                >
                  {lastPackage ? lastPackage.status : "No Active Package"}
                </span>
              </div>
            </div>
          </div>
        </div>


        {/* Card Body - Contact Info */}
        <div className="px-6 pb-4">
          <div className="space-y-2">
            {manager.email && (
              <div className="flex items-center text-sm text-gray-600">
                <FaEnvelope className="mr-2 text-gray-400" />
                <span className="truncate">{manager.email}</span>
              </div>
            )}
            {manager.phone && (
              <div className="flex items-center text-sm text-gray-600">
                <FaPhone className="mr-2 text-gray-400" />
                <span>{manager.phone}</span>
              </div>
            )}
            {manager.department && (
              <div className="flex items-center text-sm text-gray-600">
                <FaUser className="mr-2 text-gray-400" />
                <span>{manager.department}</span>
              </div>
            )}
          </div>
        </div>

        {/* Progress Bar for cards usage */}
        {activePackage && cardLimit > 0 && (
          <div className="px-6 pb-2">
            <div className="flex items-center justify-between mb-1">
              <span className="text-xs text-gray-500 font-semibold">Cards Usage</span>
            </div>
            <div className="relative w-full">
              {/* الفقاعة بجانب الماوس */}
              {showCardsPercent && (
                <div
                  className="absolute z-20 flex flex-col items-center pointer-events-none select-none"
                  style={{ left: cardsPercentPos.x, top: cardsPercentPos.y - 32, transform: 'translateX(-50%)' }}
                >
                  <div className="bg-slate-800 shadow-lg border border-slate-900 rounded-xl px-2 py-0.5 text-xs font-bold text-blue-300 flex items-center gap-1 animate-fade-in-up">
                    <span>{cardLimit > 0 ? Math.round((cardsCount / cardLimit) * 100) : 0}%</span>
                  </div>
                  <div className="w-2 h-2 bg-slate-800 border-l border-b border-slate-900 rotate-45 -mt-1 shadow-sm"></div>
                </div>
              )}
              <div
                ref={cardsBarRef}
                className="w-full h-3 bg-gray-200 rounded-full overflow-hidden relative shadow-inner group/progress cursor-pointer"
                style={{ position: "relative" }}
                onMouseEnter={() => setShowCardsPercent(true)}
                onMouseLeave={() => setShowCardsPercent(false)}
                onMouseMove={e => {
                  const rect = e.currentTarget.getBoundingClientRect();
                  setCardsPercentPos({ x: e.clientX - rect.left, y: e.clientY - rect.top });
                }}
              >
                {(() => {
                  let percent = cardLimit > 0 ? Math.round((cardsCount / cardLimit) * 100) : 0;
                  let barColor = percent >= 90 ? 'linear-gradient(90deg, #ef4444 0%, #f87171 100%)' : percent >= 70 ? 'linear-gradient(90deg, #facc15 0%, #fde68a 100%)' : 'linear-gradient(90deg, #22c55e 0%, #16a34a 100%)';
                  return (
              <div
                className={`h-full transition-all duration-700 ease-in-out rounded-full relative animate-pulse`}
                style={{
                  width: `${percent}%`,
                  minWidth: percent > 0 ? 12 : 0,
                        background: barColor,
                  boxShadow: '0 2px 8px 0 rgba(0,0,0,0.10)',
                  position: 'relative',
                  overflow: 'hidden',
                }}
              >
                {/* shimmer effect */}
                <div
                  className="absolute top-0 left-0 h-full w-full"
                  style={{
                    background: 'linear-gradient(120deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.35) 50%, rgba(255,255,255,0.10) 100%)',
                    animation: 'shimmer 1.8s infinite linear',
                    zIndex: 1,
                    pointerEvents: 'none',
                  }}
                />
              </div>
                  );
                })()}
                <style>{`
                  @keyframes shimmer {
                    0% { transform: translateX(-100%); }
                    100% { transform: translateX(100%); }
                  }
                `}</style>
              </div>
            </div>
            {/* Progress Bar for Subscription Duration */}
            {activePackage.purchased_at && activePackage.expiry_date && (
              <div className="mt-3">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-xs text-gray-500 font-semibold">Subscription Duration</span>
                  <span className="text-xs text-blue-500 font-bold">
                    {(() => {
                      if (!activePackage.expiry_date) return '';
                      const now = new Date();
                      const end = new Date(activePackage.expiry_date);
                      const diff = Math.ceil((end - now) / (1000 * 60 * 60 * 24));
                      return diff > 0 ? `${diff} days left` : 'Expired';
                    })()}
                  </span>
                </div>
                <div className="relative w-full">
                  

                  {showSubPercent && (
                    <div
                      className="absolute z-20 flex flex-col items-center pointer-events-none select-none"
                      style={{ left: subPercentPos.x, top: subPercentPos.y - 32, transform: 'translateX(-50%)' }}
                    >
                      <div className="bg-slate-800 shadow-lg border border-slate-900 rounded-xl px-2 py-0.5 text-xs font-bold text-blue-300 flex items-center gap-1 animate-fade-in-up">
                        <span>{subscriptionPercent}%</span>
                      </div>
                      <div className="w-2 h-2 bg-slate-800 border-l border-b border-slate-900 rotate-45 -mt-1 shadow-sm"></div>
                    </div>
                  )}
                  <div
                    ref={subscriptionBarRef}
                    className="w-full h-3 bg-gray-200 rounded-full overflow-hidden relative shadow-inner group/progress cursor-pointer"
                    style={{ position: "relative" }}
                    onMouseEnter={() => setShowSubPercent(true)}
                    onMouseLeave={() => setShowSubPercent(false)}
                    onMouseMove={e => {
                      const rect = e.currentTarget.getBoundingClientRect();
                      setSubPercentPos({ x: e.clientX - rect.left, y: e.clientY - rect.top });
                    }}
                  >
                    {(() => {
                      let percent = subscriptionPercent;
                      let barColor = percent >= 90 ? 'linear-gradient(90deg, #ef4444 0%, #f87171 100%)' : percent >= 70 ? 'linear-gradient(90deg, #facc15 0%, #fde68a 100%)' : 'linear-gradient(90deg, #22c55e 0%, #16a34a 100%)';
                      return (
                        <div
                          className={`h-full transition-all duration-700 ease-in-out rounded-full relative animate-pulse`}
                style={{
                            width: `${percent}%`,
                            minWidth: percent > 0 ? 12 : 0,
                            background: barColor,
                            boxShadow: '0 2px 8px 0 rgba(0,0,0,0.10)',
                            position: 'relative',
                            overflow: 'hidden',
                          }}
                        >
                          {/* shimmer effect */}
                          <div
                            className="absolute top-0 left-0 h-full w-full"
                            style={{
                              background: 'linear-gradient(120deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.35) 50%, rgba(255,255,255,0.10) 100%)',
                              animation: 'shimmer 1.8s infinite linear',
                              zIndex: 1,
                              pointerEvents: 'none',
                            }}
                          />
                        </div>
                      );
                    })()}
              <style>{`
                @keyframes shimmer {
                  0% { transform: translateX(-100%); }
                  100% { transform: translateX(100%); }
                }
              `}</style>
                  </div>
                </div>
                <div className="flex justify-between mt-1 text-xs text-gray-500">
                  <span>Start: {activePackage.purchased_at ? new Date(activePackage.purchased_at).toLocaleDateString() : '--'}</span>
                  <span>End: {activePackage.expiry_date ? new Date(activePackage.expiry_date).toLocaleDateString() : '--'}</span>
                </div>
              </div>
            )}
          </div>
        )}
        {/* Card Footer - Actions */}
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-100 mt-auto">
          <div className="flex justify-between items-center">
            <span className="text-xs text-gray-500">
              ID: {manager.id}
            </span>
            <div className="flex space-x-2">
              {/* Action buttons */}
              <Tooltip target={`.gift-btn-${manager.id}`} content="Gift Package" position="top" />
              <button
                className={`gift-btn-${manager.id} p-2 rounded-lg transition-colors duration-200`}
                style={{
                  backgroundColor: '#f3e8ff',
                  color: '#9333ea',
                  border: 'none'
                }}
                onMouseEnter={(e) => e.target.style.backgroundColor = '#e9d5ff'}
                onMouseLeave={(e) => e.target.style.backgroundColor = '#f3e8ff'}
                onClick={(e) => {
                  e.stopPropagation();
                  openGiftDialog(manager);
                }}
              >
                <FaGift size={14} />
              </button>

              <Tooltip target={`.card-btn-${manager.id}`} content="Create Card" position="top" />
              <button
                className={`card-btn-${manager.id} p-2 rounded-lg transition-colors duration-200`}
                style={{
                  backgroundColor: '#dbeafe',
                  color: '#2563eb',
                  border: 'none'
                }}
                onMouseEnter={(e) => e.target.style.backgroundColor = '#bfdbfe'}
                onMouseLeave={(e) => e.target.style.backgroundColor = '#dbeafe'}
                onClick={(e) => {
                  e.stopPropagation();
                  setSelectedMember(manager);
                  dialogHandler("CreateCardToManagerForm", true);
                }}
              >
                <HiOutlineCreditCard size={14} />
              </button>

              <Tooltip target={`.edit-btn-${manager.id}`} content="Edit Manager" position="top" />
              <button
                className={`edit-btn-${manager.id} p-2 rounded-lg transition-colors duration-200`}
                style={{
                  backgroundColor: '#fef3c7',
                  color: '#d97706',
                  border: 'none'
                }}
                onMouseEnter={(e) => e.target.style.backgroundColor = '#fde68a'}
                onMouseLeave={(e) => e.target.style.backgroundColor = '#fef3c7'}
                onClick={(e) => {
                  e.stopPropagation();
                  editMember(manager);
                }}
              >
                <FiEdit size={14} />
              </button>

              {manager.id.toString() !== localStorage.getItem("user_id") && (
                <>
                  <Tooltip target={`.delete-btn-${manager.id}`} content="Delete Manager" position="top" />
                  <button
                    className={`delete-btn-${manager.id} p-2 rounded-lg transition-colors duration-200`}
                    style={{
                      backgroundColor: '#fee2e2',
                      color: '#dc2626',
                      border: 'none'
                    }}
                    onMouseEnter={(e) => e.target.style.backgroundColor = '#fecaca'}
                    onMouseLeave={(e) => e.target.style.backgroundColor = '#fee2e2'}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteClick(manager);
                    }}
                  >
                    <TfiTrash size={14} />
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </motion.div>
    );
  };

  ManagerCard.propTypes = {
    manager: PropTypes.shape({
      id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      name: PropTypes.string,
      image: PropTypes.string,
      company_name: PropTypes.string,
      email: PropTypes.string,
      phone: PropTypes.string,
      department: PropTypes.string,
      packages: PropTypes.arrayOf(PropTypes.object),
    }).isRequired,
  };

  return (
    <>
      <Toast ref={toastRef} />
      <div className="w-full mt-8">
        {/* Header */}
        <Header
          searchQuery={searchQuery}
          handleSearchChange={handleSearchChange}
          isMobile={isMobile}
          createMember={createMember}
        />

        {/* Cards Grid */}
        {tableLoading ? (
          <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden animate-pulse">
                <div className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-16 bg-gray-300 rounded-full"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-300 rounded w-1/2"></div>
                    </div>
                  </div>
                </div>
                <div className="px-6 pb-4">
                  <div className="space-y-2">
                    <div className="h-3 bg-gray-300 rounded w-full"></div>
                    <div className="h-3 bg-gray-300 rounded w-2/3"></div>
                  </div>
                </div>
                <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
                  <div className="flex justify-between items-center">
                    <div className="h-3 bg-gray-300 rounded w-1/4"></div>
                    <div className="flex space-x-2">
                      <div className="w-8 h-8 bg-gray-300 rounded-lg"></div>
                      <div className="w-8 h-8 bg-gray-300 rounded-lg"></div>
                      <div className="w-8 h-8 bg-gray-300 rounded-lg"></div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className={`grid gap-6 w-full min-h-[200px] ${isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'}`}>
            {(data || []).map((manager) => (
              <ManagerCard key={manager.id} manager={manager} />
            ))}
          </div>
        )}

        {/* Pagination Controls */}
        {!searchQuery.trim() && totalRecords > 0 && (
          <div className="flex justify-center items-center gap-4 mt-8">
           
            <button
              onClick={goToPrevPage}
              disabled={lazyManagersParams.first === 0}
              className="px-4 py-2 bg-white border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Previous
            </button>
            <span className="px-4 py-2 text-sm text-gray-600">
              Page {lazyManagersParams.page + 1} of {Math.ceil(totalRecords / lazyManagersParams.rows)}
            </span>
            <button
              onClick={goToNextPage}
              disabled={lazyManagersParams.first + lazyManagersParams.rows >= totalRecords}
              className="px-4 py-2 bg-white border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Next
            </button>
             <span className="text-sm text-gray-600 mr-4">
              {`showing ${totalRecords === 0 ? 0 : lazyManagersParams.first + 1} to ${Math.min(lazyManagersParams.first + lazyManagersParams.rows, totalRecords)} of ${totalRecords} managers`}
            </span>
            <div className="flex items-center gap-2 ml-4">
              <span className="text-sm">Cards per page:</span>
              <Dropdown
                value={lazyManagersParams.rows}
                options={[5, 25, 50, 100].map(opt => ({ label: opt, value: opt }))}
                onChange={e => handleRowsPerPageChange({ target: { value: e.value } })}
                style={{ minWidth: 100, width: 130 }}
                placeholder="Choose"
              />
            </div>
          </div>
        )}

        {/* Empty State */}
        {allManagers.length === 0 && !tableLoading && (
          <div className="text-center py-12">
            <FaUser className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No managers found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchQuery ? 'Try adjusting your search terms.' : 'Get started by adding a new manager.'}
            </p>
          </div>
        )}

        {/* Loading State */}
        {/* This block is now redundant as loading state is handled by the main conditional */}
        {/* {tableLoading && (
          <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden animate-pulse">
                <div className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-16 bg-gray-300 rounded-full"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-300 rounded w-1/2"></div>
                    </div>
                  </div>
                </div>
                <div className="px-6 pb-4">
                  <div className="space-y-2">
                    <div className="h-3 bg-gray-300 rounded w-full"></div>
                    <div className="h-3 bg-gray-300 rounded w-2/3"></div>
                  </div>
                </div>
                <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
                  <div className="flex justify-between items-center">
                    <div className="h-3 bg-gray-300 rounded w-1/4"></div>
                    <div className="flex space-x-2">
                      <div className="w-8 h-8 bg-gray-300 rounded-lg"></div>
                      <div className="w-8 h-8 bg-gray-300 rounded-lg"></div>
                      <div className="w-8 h-8 bg-gray-300 rounded-lg"></div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )} */}

        {/* Manager Detail Modal */}
        <ManagerDetailModal
          visible={managerDetailModalVisible}
          onHide={() => setManagerDetailModalVisible(false)}
          selectedManager={selectedManagerForModal}
          managerActivePackages={managerActivePackages}
          loadingManagerPackages={loadingManagerPackages}
          managerPackagesError={managerPackagesError}
          managerPackageHistory={managerPackageHistory}
          loadingManagerPackageHistory={loadingManagerPackageHistory}
          managerPackageHistoryError={managerPackageHistoryError}
          onGiftPackage={openGiftDialog}
          onCreateCard={(manager) => {
            setSelectedMember(manager);
            dialogHandler("CreateCardToManagerForm", true);
          }}
          onEditManager={editMember}
          onDeleteManager={handleDeleteClick}
        />

        <Dialog
          visible={giftDialogVisible}
          style={{ width: "650px" }} // Increased from 500px to 650px
          header="Assign Package to User"
          modal
          className="p-fluid"
          footer={
            <div className="flex justify-between w-full pt-3"> {/* Changed from justify-end to justify-between */}
              <Button
                label="Cancel"
                icon="pi pi-times"
                onClick={() => setGiftDialogVisible(false)}
                className="p-button-danger p-button-rounded"
              />
              <Button
                label="Assign Package"
                icon="pi pi-gift"
                onClick={handleGiftSubmit}
                loading={giftLoading}
                className="p-button-success p-button-rounded"
              />
            </div>
          }
          onHide={() => setGiftDialogVisible(false)}
        >
          <div className="gift-form p-4">
            {/* Payment method icon animation */}
            <div className="flex justify-center mb-4">
              <motion.div
                key={animationKey}
                initial={{ scale: 0.5, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5, type: "spring", bounce: 0.4 }}
                className="p-6 rounded-full bg-gray-100" // Increased padding
              >
                {formData.payment_method === "gift" ? (
                  <motion.div
                    animate={{ rotate: [0, 10, -10, 10, 0] }}
                    transition={{ duration: 1, repeat: Infinity, repeatType: "loop", ease: "easeInOut" }}
                  >
                    <FaGift size={80} className="text-purple-500" /> {/* Increased from 50 to 80 */}
                  </motion.div>
                ) : (
                  <motion.div
                    animate={{ y: [0, -5, 0] }}
                    transition={{ duration: 1, repeat: Infinity, repeatType: "loop", ease: "easeInOut" }}
                  >
                    <BsBank2 size={80} className="text-blue-500" /> {/* Increased from 50 to 80 */}
                  </motion.div>
                )}
              </motion.div>
            </div>

            <div className="field mb-4">
              <label htmlFor="package" className="block text-sm font-medium mb-2">
                Select Package
              </label>
              <Dropdown
                id="package"
                value={formData.package_id}
                options={availablePackages.map(pkg => ({
                  label: `${pkg.name} (${pkg.card_limit} cards)`,
                  value: pkg.id
                }))}
                onChange={(e) => {
                  const selectedPkg = availablePackages.find(p => p.id === e.value);
                  setFormData({
                    ...formData,
                    package_id: e.value,
                    total_price: formData.payment_method === "bank_transfer" ?
                      (formData.duration === 12 ? selectedPkg?.yearly_price : selectedPkg?.monthly_price) : 0
                  });
                  setSelectedPackage(selectedPkg);
                }}
                placeholder="Select a package"
                className="w-full"
                disabled={giftLoading}
                required
                optionLabel="label"
              />
              {selectedPackage && (
                <div className="mt-2 text-sm text-gray-600">
                  <div className="flex justify-between">
                    <span>Monthly Price:</span>
                    <span>${selectedPackage.monthly_price}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Yearly Price:</span>
                    <span>${selectedPackage.yearly_price}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Card Types:</span>
                    <span>{selectedPackage.card_type_names.join(', ')}</span>
                  </div>
                </div>
              )}
            </div>

            <div className="field mb-4">
              <label htmlFor="duration" className="block text-sm font-medium mb-2">
                Subscription Duration
              </label>
              <Dropdown
                id="duration"
                value={formData.duration}
                options={[
                  { label: "Monthly (1 month)", value: 1 },
                  { label: "Quarterly (3 months)", value: 3 },
                  { label: "Semi-Annual (6 months)", value: 6 },
                  { label: "Annual (12 months)", value: 12 }
                ]}
                onChange={(e) => {
                  // Update duration
                  setFormData(prev => ({ ...prev, duration: e.value }));

                  // Suggest a price if package is selected and payment method is bank transfer
                  if (selectedPackage && formData.payment_method === "bank_transfer") {
                    let suggestedPrice;

                    if (e.value === 12) {
                      suggestedPrice = selectedPackage.yearly_price;
                    } else if (e.value === 6) {
                      suggestedPrice = (selectedPackage.monthly_price * 6) * 0.9; // 10% discount
                    } else if (e.value === 3) {
                      suggestedPrice = (selectedPackage.monthly_price * 3) * 0.95; // 5% discount
                    } else {
                      suggestedPrice = selectedPackage.monthly_price;
                    }

                    // Suggest the price but don't force it - user can still edit manually
                    setFormData(prev => ({
                      ...prev,
                      duration: e.value,
                      total_price: suggestedPrice
                    }));
                  }
                }}
                placeholder="Select duration"
                className="w-full"
                disabled={giftLoading}
              />
            </div>

            <div className="field mb-4">
              <label htmlFor="payment_method" className="block text-sm font-medium mb-2">
                Payment Method
              </label>
              <Dropdown
                id="payment_method"
                value={formData.payment_method}
                options={[
                  { label: "Gift (Free)", value: "gift" },
                  { label: "Bank Transfer", value: "bank_transfer" }
                ]}
                onChange={(e) => {
                  setFormData({ ...formData, payment_method: e.value });
                  // Trigger animation when payment method changes
                  setAnimationKey(prev => prev + 1);
                }}
                className="w-full"
                disabled={giftLoading}
              />
            </div>

            {formData.payment_method === "bank_transfer" && (
              <div className="field mb-4">
                <label htmlFor="total_price" className="block text-sm font-medium mb-2">
                  Total Price
                </label>
                <InputNumber
                  id="total_price"
                  value={formData.total_price}
                  onValueChange={(e) => setFormData({ ...formData, total_price: e.value })}
                  mode="currency"
                  currency="USD"
                  locale="en-US"
                  className="w-full"
                  disabled={giftLoading}
                  required
                />
                <small className="text-blue-600 mt-1 block">
                  You can adjust this price manually if needed.
                </small>
              </div>
            )}

            <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <p className="text-sm text-blue-800">
                <i className="pi pi-info-circle mr-2"></i>
                {formData.payment_method === "gift"
                  ? "This will provide a free package to the user."
                  : "This will assign a paid package via bank transfer."}
              </p>
            </div>
          </div>
        </Dialog>

        {openDialog?.addMember && (
          <AddMemberDialog data={selectedMember} actionType={actionType} />
        )}
        {openDialog?.createGroup && (
          <AssignGroupDialog data={[]} />
        )}
        {openDialog?.updateGroup && <AssignGroupDialog />}

        {openDialog?.CreateCardToManagerForm &&
          selectedMember &&
          selectedMember.packages && (
            <CreateCardToManagerForm
              userId={selectedMember.id}
              packages={selectedMember.packages.filter(
                (pkg) => pkg.status === "active"
              )}
            />
          )}
      </div>
      {/* ConfirmDialog for delete confirmation */}
      <ConfirmDialog group="headless"
        content={(options) => (
          <div className="flex flex-col items-center p-5">
            <i className="pi pi-exclamation-triangle text-6xl text-yellow-500 mb-3"/>
            <span className="text-xl font-bold mb-2">{options.message}</span>
            <div className="flex gap-3">
              <button className="p-button p-component" onClick={options.accept}>
                Yes
              </button>
              <button className="p-button p-component p-button-outlined" onClick={options.reject}>
                No
              </button>
            </div>
          </div>
        )}
      />


    </>
  );
}

export default ManagersDataTable;


