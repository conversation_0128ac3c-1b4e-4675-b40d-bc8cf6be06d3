import { useState } from 'react';
import { Dropdown } from 'primereact/dropdown';

import { useDesignSpace } from '@contexts/DesignSpaceContext';
import { fieldsOptions } from '@constants/DesignSpaceConfig';
import { measureText } from './TextSettings';

function FieldsDropdown() {
    const { addElement, getActiveTextStyle } = useDesignSpace();
    const [selectedField, setSelectedField] = useState("");

    const onChangeHandler = (val) => {
        const activeStyle = getActiveTextStyle && getActiveTextStyle();
        let customProps = {};
        if (activeStyle) {
            const pureStyle = JSON.parse(JSON.stringify(activeStyle));
            delete pureStyle.value;
            delete pureStyle.id;
            customProps = { ...pureStyle };
        }
        // قياس أبعاد النص بناءً على style
        const { width, height } = measureText(
            val,
            customProps.fontSize || 16,
            customProps.fontFamily || 'Arial, sans-serif',
            customProps.fontWeight || 'normal',
            customProps.fontStyle || 'normal',
            customProps.lineHeight || 1.2
        );
        customProps.width = Math.max(width, 40);
        customProps.height = Math.max(height, 24);
        addElement("text", val, customProps);
        setSelectedField("");
    }
 
    return (
        <>
            <Dropdown
                defaultValue={fieldsOptions[0].dimension}
                className='rounded-[6px] me-3 text-[black] w-full'
                optionLabel="label"
                optionValue="value"
                value={selectedField}
                options={fieldsOptions}
                onChange={(e) => { setSelectedField(e.value); onChangeHandler(e.value); }}
                placeholder="select field ..." />
        </>
    )
}

export default FieldsDropdown