import { useDesignSpace } from "@contexts/DesignSpaceContext";
import { isEmpty } from "lodash";
import { useEffect, useRef, useState } from "react";
import PropTypes from 'prop-types';

export default function Element({ el, scaleFactor = 1, userData = {}, selectedIds = [] }) {

    // Function to replace variables with user data
    const replaceVariables = (text) => {
        if (!text || typeof text !== 'string') return text;
        
        return text.replace(/\{\{(\w+)\}\}/g, (match, variable) => {
            // Handle different variable formats
            const cleanVariable = variable.replace(/^\$/, ''); // Remove $ prefix if exists
            const replacement = userData[cleanVariable];
            
            // If we found a replacement value, return it without any brackets
            if (replacement !== undefined && replacement !== null) {
                return replacement;
            }
            
            // If no replacement found, return the variable name without brackets
            return cleanVariable;
        });
    };

    // Check if element contains variables (either has user-data class or contains {{}})
    const hasVariables = el.className === "user-data" || 
                        (el.value && typeof el.value === 'string' && el.value.includes('{{'));

    if (hasVariables) {
        return <UserDataElement el={el} scaleFactor={scaleFactor} userData={userData} />
    }

    switch (el.type) {
        case "img":
            // If it's an avatar and we have user data, use the user's image
            if (!isEmpty(userData) && el.value === "avatar") {
                el = { ...el, value: userData?.image || "https://www.gravatar.com/avatar/?d=mp" };
            }
            return <Image el={el} />

        case "qr":
            return <QrImage el={el} />

        case "icon":
            return <Icon el={el} />

        case "text":
            // Handle dynamic fields for text elements
            if (el.isDynamicField && !isEmpty(userData)) {
                // Replace the field value with actual user data
                const fieldValue = userData[el.fieldKey] || el.value;
                el = { ...el, value: fieldValue };
            } else if (!isEmpty(userData) && el.value) {
                // For regular text elements, try to replace variables
                el = { ...el, value: userData?.[el.value] || el.value };
            }
            
            // Always replace any remaining {{}} variables in the text
            if (el.value && typeof el.value === 'string' && el.value.includes('{{')) {
                el = { ...el, value: replaceVariables(el.value) };
            }
            
            return <Text el={el} scaleFactor={scaleFactor} selectedIds={selectedIds} />

        case "label":
            return <Label el={el} scaleFactor={scaleFactor} />

        case "shape":
            return <Shape el={el} />

        case "line":
            return <Line el={el} />

        case "frame":
            return <Frame el={el} />

        case "sticker":
            return <Sticker el={el} />

        case "gradient":
            return <Gradient el={el} />

        case "svg":
            return <SVGElement el={el} />

        default:
            break;
    }
}

Element.propTypes = {
    el: PropTypes.object.isRequired,
    scaleFactor: PropTypes.number,
    userData: PropTypes.object,
    selectedIds: PropTypes.array
};

const Image = ({ el }) => {
    // objectPosX, objectPosY: نسبة مئوية (0-100)
    const objectPosX = el.objectPosX || 0;
    const objectPosY = el.objectPosY || 0;

    return (
        <img
            loading="lazy"
            src={el.value}
            alt="holder"
            draggable={false}
            style={{
                borderRadius: el.borderRadius,
                width: "100%",
                height: "100%",
                objectFit: 'cover',
                objectPosition: `${objectPosX * 100}% ${objectPosY * 100}%`,
                willChange: 'transform',
                transition: 'none',
                ...(el.style || {})
            }}
        />
    )
}

Image.propTypes = {
    el: PropTypes.object.isRequired
};

const QrImage = ({ el }) => {
    return (
        <img
            loading="lazy"
            src={el.value}
            alt="Qr Code"
            style={{
                width: "100%",
                height: "100%",
            }}
        />
    )
}

QrImage.propTypes = {
    el: PropTypes.object.isRequired
};

const Label = ({ el, scaleFactor }) => {
    const { updateElement } = useDesignSpace();

    // Function to clean brackets for editing
    const cleanValueForEditing = (value) => {
        if (!value || typeof value !== 'string') return value;
        // Remove {{}} brackets for editing
        return value.replace(/\{\{(\w+)\}\}/g, '$1');
    };

    // Function to restore brackets when saving
    const restoreBrackets = (value) => {
        if (!value || typeof value !== 'string') return value;
        // If it's a field name without brackets, add them back
        const fieldNames = ['name', 'type', 'position', 'department', 'custom_field_1', 'custom_field_2', 'custom_field_3', 'custom_field_4', 'custom_field_5', 'custom_field_6', 'custom_field_7', 'custom_field_8', 'custom_field_9', 'custom_field_10'];
        if (fieldNames.includes(value)) {
            return `{{${value}}}`;
        }
        return value;
    };

    return (
        <input
            type="text"
            value={cleanValueForEditing(el.value) ?? ""}
            placeholder="Enter your text here"
            onChange={(e) => updateElement(el.id, { value: restoreBrackets(e.target.value) })}
            style={{
                textDecoration: el.isUnderlined ? "underline" : "none",
                backgroundColor: "transparent",
                fontWeight: el.isBold ? "bold" : "normal",
                fontFamily: el.fontFamily,
                fontStyle: el.isItalic ? "italic" : "normal",
                textTransform: el.textTransform,
                textAlign: el.textAlign,
                fontSize: el.fontSize * scaleFactor,
                overflow: "visible",
                padding: "2px",
                color: el.color,
                height: "100%",
                width: "100%",
            }} />
    )
}

Label.propTypes = {
    el: PropTypes.object.isRequired,
    scaleFactor: PropTypes.number
};

const Text = ({ el, scaleFactor = 1, selectedIds = [] }) => {
    const { updateElement, designSpaceRef, setSelectedElement, setSelectedIds, elements } = useDesignSpace();
    const ref = useRef();
    const caretRef = useRef(null);
    const [isAutoResizing, setIsAutoResizing] = useState(false);
    const [lastText, setLastText] = useState(el.value || '');
    const [isEditing, setIsEditing] = useState(false);
    const isDynamic = el.isDynamicField || (typeof el.value === 'string' && el.value.includes('{{'));
    const isSelected = selectedIds && selectedIds.includes(el.id);

    // دالة لكشف إذا كان النص عربيًا
    const isArabic = (text) => /[\u0600-\u06FF]/.test(text);
    const currentValue = el.value || '';
    const rtl = isArabic(currentValue);

    // عند التحديد أو إلغاء التحديد، أخرج من وضع التعديل
    useEffect(() => {
        if (!isSelected && isEditing) {
            setIsEditing(false);
        }
    }, [isSelected]);

    // مزامنة النص مع contentEditable عند التحديد
    useEffect(() => {
        if (isSelected && ref.current && !isDynamic) {
            if (!el.hasBeenEdited && !el.value) {
                ref.current.innerText = 'Text';
            } else {
                ref.current.innerText = el.value || '';
            }
        }
        if (!isSelected) ref.current && (ref.current.blur && ref.current.blur());
    }, [isSelected, isDynamic, el.id, el.value, el.hasBeenEdited]);

    // دالة لحفظ الـ Range (المسار داخل الشجرة)
    function saveSelection(containerEl) {
        const sel = window.getSelection();
        if (sel.rangeCount === 0) return null;
        const range = sel.getRangeAt(0);
        function getNodePath(node, root) {
            const path = [];
            while (node && node !== root) {
                let index = 0;
                let sibling = node;
                while ((sibling = sibling.previousSibling) != null) index++;
                path.unshift(index);
                node = node.parentNode;
            }
            return path;
        }
        return {
            start: { path: getNodePath(range.startContainer, containerEl), offset: range.startOffset },
            end: { path: getNodePath(range.endContainer, containerEl), offset: range.endOffset }
        };
    }

    // دالة لإرجاع الـ Range
    function restoreSelection(containerEl, savedSel) {
        if (!savedSel) return;
        const sel = window.getSelection();
        sel.removeAllRanges();
        function getNodeByPath(root, path) {
            let node = root;
            for (let i = 0; i < path.length; i++) {
                node = node.childNodes[path[i]];
                if (!node) break;
            }
            return node;
        }
        const range = document.createRange();
        const startNode = getNodeByPath(containerEl, savedSel.start.path);
        const endNode = getNodeByPath(containerEl, savedSel.end.path);

        try {
            if (
                startNode &&
                endNode &&
                savedSel.start.offset <= (startNode.textContent ? startNode.textContent.length : 0) &&
                savedSel.end.offset <= (endNode.textContent ? endNode.textContent.length : 0)
            ) {
                range.setStart(startNode, savedSel.start.offset);
                range.setEnd(endNode, savedSel.end.offset);
            } else {
                range.selectNodeContents(containerEl);
                range.collapse(false);
            }
            sel.addRange(range);
        } catch (err) {
            range.selectNodeContents(containerEl);
            range.collapse(false);
            sel.addRange(range);
        }
    }

    // عند التعديل على النص مباشرة
    const handleInput = (e) => {
        const editableDiv = e.currentTarget;
        let text = editableDiv.innerText;
        let savedSel = saveSelection(editableDiv);
        caretRef.current = savedSel;
        let insertedDefault = false;
        if (text.trim() === '') {
            text = 'Your Text';
            editableDiv.innerText = text;
            insertedDefault = true;
        }
        setIsAutoResizing(true);
        requestAnimationFrame(() => {
            if (ref.current && designSpaceRef && designSpaceRef.current) {
                const newHeight = ref.current.scrollHeight;
                const textRect = ref.current.getBoundingClientRect();
                const canvasRect = designSpaceRef.current.getBoundingClientRect();
                const bottomSpace = canvasRect.bottom - textRect.top;
                const maxAllowedHeight = Math.min(400, bottomSpace - 4);
                if (newHeight + 4 > maxAllowedHeight) {
                    if (editableDiv.innerText !== lastText) {
                        editableDiv.innerText = lastText;
                    }
                    setTimeout(() => {
                        const sel = window.getSelection();
                        const range = document.createRange();
                        range.selectNodeContents(editableDiv);
                        range.collapse(false);
                        sel.removeAllRanges();
                        sel.addRange(range);
                    }, 0);
                    setIsAutoResizing(false);
                    return;
                } else {
                    setLastText(text);
                }
                if (newHeight + 4 !== el.height) {
                    updateElement(el.id, { height: Math.min(newHeight + 4, maxAllowedHeight) });
                }
            }
            setIsAutoResizing(false);
            if (insertedDefault || !savedSel) {
                const sel = window.getSelection();
                const range = document.createRange();
                range.selectNodeContents(editableDiv);
                range.collapse(false);
                sel.removeAllRanges();
                sel.addRange(range);
            } else {
                restoreSelection(editableDiv, savedSel);
            }
        });
        updateElement(el.id, { value: text, hasBeenEdited: true });
    };

    useEffect(() => {
        if (isSelected && ref.current && caretRef.current) {
            restoreSelection(ref.current, caretRef.current);
        }
    }, [el.value]);

    const handleBlur = (e) => {
        setIsEditing(false);
        if (!isDynamic) {
            const newText = e.currentTarget.innerText;
            if (newText.trim() === '') {
                updateElement(el.id, { value: 'Your Text', hasBeenEdited: true });
                window.dispatchEvent(new CustomEvent('designTextInput', { detail: { id: el.id, value: 'Your Text' } }));
            } else {
                updateElement(el.id, { value: newText, hasBeenEdited: true });
                window.dispatchEvent(new CustomEvent('designTextInput', { detail: { id: el.id, value: newText } }));
            }
        }
    };

    // الضغط مرتين: تفعيل التعديل
    const handleDoubleClick = (e) => {
        e.stopPropagation();
        if (!isDynamic && isSelected) {
            setIsEditing(true);
            setTimeout(() => {
                ref.current && ref.current.focus && ref.current.focus();
            }, 0);
        }
    };

    // الضغط مرة واحدة: تحديد فقط
    const handleClick = (e) => {
        e.stopPropagation();
        if (!isSelected) {
            // استخدم setSelectedElement و setSelectedIds من السياق
            const element = elements.find(item => item.id === el.id);
            if (element) {
                setSelectedElement(element);
                setSelectedIds([el.id]);
            }
        }
    };

    // منع السحب أثناء التعديل
    const handleMouseDown = () => {
        // لا حاجة لأي منطق هنا حالياً
    };

    return (
        <div
            ref={ref}
            contentEditable={isEditing && !isDynamic}
            suppressContentEditableWarning
            spellCheck={false}
            onInput={handleInput}
            onBlur={handleBlur}
            onDoubleClick={handleDoubleClick}
            onClick={handleClick}
            onMouseDown={handleMouseDown}
            style={{
                width: "100%",
                height: isAutoResizing ? "auto" : el.height,
                minHeight: 24,
                maxHeight: 400,
                overflowY: "auto",
                display: isEditing ? undefined : "flex",
                alignItems: isEditing ? undefined : "center",
                justifyContent: isEditing ? undefined : "center",
                textAlign: rtl ? "right" : (el.textAlign || "center"),
                direction: rtl ? "rtl" : "ltr",
                fontSize: (el.fontSize || 16) * scaleFactor,
                fontFamily: el.fontFamily || "Arial, sans-serif",
                fontWeight: el.fontWeight || "normal",
                fontStyle: el.fontStyle || "normal",
                color: el.color || "#000000",
                backgroundColor: el.backgroundColor || "transparent",
                background: el.background || el.backgroundColor || "transparent",
                textDecoration: el.textDecoration || "none",
                textTransform: el.textTransform || "none",
                lineHeight: el.lineHeight || 1.2,
                letterSpacing: el.letterSpacing || "normal",
                whiteSpace: "pre-wrap",
                wordBreak: "break-word",
                overflow: "hidden",
                padding: "4px",
                borderRadius: el.borderRadius || 0,
                border: el.border || "none",
                boxShadow: el.boxShadow || "none",
                opacity: el.opacity || 1,
                transform: el.transform || "none",
                userSelect: isEditing ? "text" : "none",
                outline: "none",
                cursor: isEditing && !isDynamic ? "text" : "move",
                resize: "none"
            }}
            tabIndex={isSelected ? 0 : -1}
        >
            {(!el.hasBeenEdited && !currentValue) ? 'Text' : currentValue}
        </div>
    );
};

Text.propTypes = {
    el: PropTypes.object.isRequired,
    scaleFactor: PropTypes.number,
    selectedIds: PropTypes.array
};

const UserDataElement = ({ el, userData }) => {
    // Function to replace variables with user data
    const replaceVariables = (text) => {
        if (!text || typeof text !== 'string') return text;
        
        return text.replace(/\{\{(\w+)\}\}/g, (match, variable) => {
            // Handle different variable formats
            const cleanVariable = variable.replace(/^\$/, ''); // Remove $ prefix if exists
            const replacement = userData[cleanVariable];
            
            // If we found a replacement value, return it without any brackets
            if (replacement !== undefined && replacement !== null) {
                return replacement;
            }
            
            // If no replacement found, return the variable name without brackets
            return cleanVariable;
        });
    };

    // Get the content from the element
    let content = el.value || el.textContent || el.innerHTML || "Your text content here";
    
    // Replace variables if userData is provided
    if (!isEmpty(userData)) {
        content = replaceVariables(content);
    }

    return (
        <div
            className="user-data"
            style={{
                textDecoration: el.isUnderlined ? "underline" : "none",
                backgroundColor: "transparent",
                fontWeight: el.isBold ? "bold" : "normal",
                fontFamily: el.fontFamily || "Roboto",
                fontStyle: el.isItalic ? "italic" : "normal",
                textTransform: el.textTransform || "lowercase",
                textAlign: el.textAlign || "center",
                fontSize: el.fontSize || "16px",
                whiteSpace: "normal",
                wordWrap: "break-word",
                overflow: "visible",
                padding: "2px",
                color: el.color || "black",
                height: "100%",
                width: "100%",
            }}
        >
            {content}
        </div>
    )
}

UserDataElement.propTypes = {
    el: PropTypes.object.isRequired,
    userData: PropTypes.object
};

const Icon = ({ el }) => {
    return (
        <img
            loading="lazy"
            src={el.value}
            style={{
                width: "100%",
                height: "100%",
            }}
        />
    )
}

Icon.propTypes = {
    el: PropTypes.object.isRequired
};

// Shape component for rendering different shapes
const Shape = ({ el }) => {
    const shapeStyles = {
        width: "100%",
        height: "100%",
        backgroundColor: el.backgroundColor || "#4338ca",
        background: el.background || el.backgroundColor || "#4338ca",
        userSelect: "none",
    };

    switch (el.shapeType) {
        case "rectangle":
            return <div style={{ ...shapeStyles, borderRadius: "4px" }}></div>;
        case "square":
            return <div style={{ ...shapeStyles, borderRadius: "4px" }}></div>;
        case "circle":
            return <div style={{ ...shapeStyles, borderRadius: "50%" }}></div>;
        case "triangle":
            return (
                <div
                    style={{
                        width: "0",
                        height: "0",
                        borderLeft: `${el.width / 2}px solid transparent`,
                        borderRight: `${el.width / 2}px solid transparent`,
                        borderBottom: `${el.height}px solid ${el.backgroundColor || el.background || "#4338ca"}`,
                    }}
                ></div>
            );
        case "star":
            return (
                <div
                    style={{
                        ...shapeStyles,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        fontSize: Math.min(el.width, el.height) * 0.8,
                        color: el.backgroundColor || el.background || "#4338ca",
                        backgroundColor: "transparent",
                    }}
                >
                    ★
                </div>
            );
        case "heart":
            return (
                <div
                    style={{
                        ...shapeStyles,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        fontSize: Math.min(el.width, el.height) * 0.8,
                        color: el.backgroundColor || el.background || "#4338ca",
                        backgroundColor: "transparent",
                    }}
                >
                    ❤
                </div>
            );
        case "pentagon":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 100" style={{ fill: el.backgroundColor || el.background || "#4338ca" }}>
                    <polygon points="50,0 100,38 81,100 19,100 0,38" />
                </svg>
            );
        case "hexagon":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 100" style={{ fill: el.backgroundColor || el.background || "#4338ca" }}>
                    <polygon points="25,0 75,0 100,50 75,100 25,100 0,50" />
                </svg>
            );
        case "diamond":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 100" style={{ fill: el.backgroundColor || el.background || "#4338ca" }}>
                    <polygon points="50,0 100,50 50,100 0,50" />
                </svg>
            );
        case "rounded-square":
            return <div style={{ ...shapeStyles, borderRadius: "15px" }}></div>;
        case "oval":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 100" style={{ fill: el.backgroundColor || el.background || "#4338ca" }}>
                    <ellipse cx="50" cy="50" rx="50" ry="35" />
                </svg>
            );
        case "trapezoid":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 100" style={{ fill: el.backgroundColor || el.background || "#4338ca" }}>
                    <polygon points="20,0 80,0 100,100 0,100" />
                </svg>
            );
        case "parallelogram":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 100" style={{ fill: el.backgroundColor || el.background || "#4338ca" }}>
                    <polygon points="25,0 100,0 75,100 0,100" />
                </svg>
            );
        case "octagon":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 100" style={{ fill: el.backgroundColor || el.background || "#4338ca" }}>
                    <polygon points="30,0 70,0 100,30 100,70 70,100 30,100 0,70 0,30" />
                </svg>
            );
        default:
            return <div style={shapeStyles}></div>;
    }
};

Shape.propTypes = {
    el: PropTypes.object.isRequired
};

// Line component for rendering different line types
const Line = ({ el }) => {
    const lineColor = el.strokeColor || el.background || "#4338ca";
    const strokeWidth = el.strokeWidth || 2;

    switch (el.lineType) {
        case "straight":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 10" style={{ stroke: lineColor, strokeWidth: strokeWidth }}>
                    <line x1="0" y1="5" x2="100" y2="5" />
                </svg>
            );
        case "dashed":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 10" style={{ stroke: lineColor, strokeWidth: strokeWidth }}>
                    <line x1="0" y1="5" x2="100" y2="5" strokeDasharray="5,5" />
                </svg>
            );
        case "dotted":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 10" style={{ stroke: lineColor, strokeWidth: strokeWidth }}>
                    <line x1="0" y1="5" x2="100" y2="5" strokeDasharray="2,2" />
                </svg>
            );
        case "double":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 10" style={{ stroke: lineColor, strokeWidth: strokeWidth }}>
                    <line x1="0" y1="3" x2="100" y2="3" />
                    <line x1="0" y1="7" x2="100" y2="7" />
                </svg>
            );
        case "wavy":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 20" style={{ stroke: lineColor, strokeWidth: strokeWidth, fill: "none" }}>
                    <path d="M0,10 Q25,0 50,10 T100,10" />
                </svg>
            );
        case "zigzag":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 20" style={{ stroke: lineColor, strokeWidth: strokeWidth, fill: "none" }}>
                    <path d="M0,10 L25,0 L50,10 L75,0 L100,10" />
                </svg>
            );
        case "curved":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 20" style={{ stroke: lineColor, strokeWidth: strokeWidth, fill: "none" }}>
                    <path d="M0,10 Q50,0 100,10" />
                </svg>
            );
        case "arrow":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 20" style={{ stroke: lineColor, strokeWidth: strokeWidth, fill: "none" }}>
                    <line x1="0" y1="10" x2="80" y2="10" />
                    <polygon points="80,5 100,10 80,15" fill={lineColor} />
                </svg>
            );
        case "double-arrow":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 20" style={{ stroke: lineColor, strokeWidth: strokeWidth, fill: "none" }}>
                    <polygon points="20,5 0,10 20,15" fill={lineColor} />
                    <line x1="20" y1="10" x2="80" y2="10" />
                    <polygon points="80,5 100,10 80,15" fill={lineColor} />
                </svg>
            );
        default:
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 10" style={{ stroke: lineColor, strokeWidth: strokeWidth }}>
                    <line x1="0" y1="5" x2="100" y2="5" />
                </svg>
            );
    }
};

Line.propTypes = {
    el: PropTypes.object.isRequired
};

// Frame component for rendering different frame types
const Frame = ({ el }) => {
    const frameColor = el.borderColor || el.background || "#4338ca";
    const borderWidth = el.borderWidth || 2;
    
    const frameStyles = {
        width: "100%",
        height: "100%",
        border: `${borderWidth}px solid ${frameColor}`,
        backgroundColor: "transparent",
        userSelect: "none",
    };

    switch (el.frameType) {
        case "rectangle":
            return <div style={{ ...frameStyles, borderRadius: "4px" }}></div>;
        case "square":
            return <div style={{ ...frameStyles, borderRadius: "4px" }}></div>;
        case "circle":
            return <div style={{ ...frameStyles, borderRadius: "50%" }}></div>;
        case "heart":
            return (
                <div
                    style={{
                        width: "100%",
                        height: "100%",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        color: frameColor,
                        fontSize: Math.min(el.width, el.height) * 0.8,
                    }}
                >
                    ♡
                </div>
            );
        default:
            return <div style={frameStyles}></div>;
    }
};

Frame.propTypes = {
    el: PropTypes.object.isRequired
};

// Sticker component for rendering different sticker types
const Sticker = ({ el }) => {
    // Emoji mapping for stickers
    const emojiMap = {
        // Emoji stickers
        "smile": "😊",
        "laugh": "😂",
        "love": "😍",
        "cool": "😎",
        "wink": "😉",
        "think": "🤔",
        "wow": "😮",
        "sad": "😢",
        "angry": "😡",

        // Symbol stickers
        "star": "⭐",
        "heart": "❤️",
        "fire": "🔥",
        "check": "✅",
        "cross": "❌",
        "warning": "⚠️",
        "lightning": "⚡",
        "music": "🎵",
        "trophy": "🏆",

        // Object stickers
        "gift": "🎁",
        "balloon": "🎈",
        "camera": "📷",
        "phone": "📱",
        "computer": "💻",
        "bulb": "💡",
        "money": "💰",
        "rocket": "🚀",
        "clock": "🕒",

        // Default for backward compatibility
        "arrow": "➡️",
        "shapes": "🔶"
    };

    return (
        <div
            style={{
                width: "100%",
                height: "100%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                fontSize: Math.min(el.width, el.height) * 0.6,
                userSelect: "none",
            }}
        >
            {emojiMap[el.stickerType] || "🔹"}
        </div>
    );
};

Sticker.propTypes = {
    el: PropTypes.object.isRequired
};

// Gradient component for rendering gradient backgrounds
const Gradient = ({ el }) => {
    return (
        <div
            style={{
                width: "100%",
                height: "100%",
                background: el.style?.background || "linear-gradient(45deg, #1e3c72, #2a5298)",
                borderRadius: "8px",
                userSelect: "none",
            }}
        ></div>
    );
};

Gradient.propTypes = {
    el: PropTypes.object.isRequired
};

// SVG component for rendering complex SVG elements
const SVGElement = ({ el }) => {
    // Check if the SVG content is available
    if (!el.value) {
        return (
            <div
                style={{
                    width: "100%",
                    height: "100%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    backgroundColor: "#f0f0f0",
                    color: "#666",
                    border: "1px dashed #999",
                    borderRadius: "4px",
                    padding: "8px",
                    fontSize: "12px",
                    textAlign: "center"
                }}
            >
                SVG content not available
            </div>
        );
    }

    // اجعل svg دائماً 100% عرض وارتفاع
    let fixedSvg = el.value.replace(
      /<svg([^>]*)>/i,
      (match, attrs) => {
        let newAttrs = attrs;
        newAttrs = newAttrs.replace(/width="[^"]*"/i, 'width="100%"');
        newAttrs = newAttrs.replace(/height="[^"]*"/i, 'height="100%"');
        if (!/width=/i.test(newAttrs)) newAttrs += ' width="100%"';
        if (!/height=/i.test(newAttrs)) newAttrs += ' height="100%"';
        return `<svg${newAttrs}>`;
      }
    );

    return (
        <div
            style={{
                width: "100%",
                height: "100%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center"
            }}
            dangerouslySetInnerHTML={{ __html: fixedSvg }}
        />
    );
};

SVGElement.propTypes = {
    el: PropTypes.object.isRequired
};