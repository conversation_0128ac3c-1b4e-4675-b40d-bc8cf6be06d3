import { useGetCardsTypes } from '@quires';
import Container from '@components/Container';
import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import CreateTypeForm from '../Cards/CreateTypeForm';
import { Toast } from 'primereact/toast';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';
import { motion, AnimatePresence } from 'framer-motion';
import { FiChevronRight, FiSearch, FiTrash } from 'react-icons/fi';
import CardCarousel from './components/CardCarousel';

function CardsIndex() {
  const { data, isLoading, refetch } = useGetCardsTypes();
  const [isCreateTypeModalOpen, setIsCreateTypeModalOpen] = useState(false);
  const [editData, setEditData] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [localData, setLocalData] = useState([]);
  const [selectedCard, setSelectedCard] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [isMobile, setIsMobile] = useState(false);
  const toast = useRef(null);
  const backendUrl = import.meta.env.VITE_BACKEND_URL;
  const token = localStorage.getItem("token");

  // Mobile detection useEffect
  useEffect(() => {
    const handleResize = () => {
      const mobileView = window.innerWidth < 768;
      setIsMobile(mobileView);
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    if (data) {
      setLocalData(data);
    }
  }, [data]);

  // Filter cards based on search term (memoized for performance)
  const filteredCards = useMemo(() => {
    if (!searchTerm.trim()) return localData;

    const searchLower = searchTerm.toLowerCase();
    return localData.filter(card =>
      card.name?.toLowerCase().includes(searchLower) ||
      card.type_of_connection?.toLowerCase().includes(searchLower)
    );
  }, [localData, searchTerm]);

  const openCreateTypeModal = () => {
    setIsEditMode(false);
    setIsCreateTypeModalOpen(true);
    setSelectedCard(null);
  };

  const handleEdit = (cardData) => {
    setEditData(cardData);
    setIsEditMode(true);
    setIsCreateTypeModalOpen(true);
  };

  const resetEditMode = () => {
    setIsEditMode(false);
    setEditData(null);
  };

  const handleCardSelect = useCallback((card) => {
    setSelectedCard(card);
  }, []);

  const handleCardEdit = useCallback((card) => {
    handleEdit(card);
  }, []);

  const handleCardDelete = useCallback((card) => {
    handleDelete(card.id);
  }, []);

  const handleDelete = (id) => {
    confirmDialog({
      message: 'Are you sure you want to delete this card type?',
      header: 'Confirmation',
      icon: 'pi pi-exclamation-triangle',
      accept: () => deleteCardType(id),
      reject: () => {}
    });
  };

  const deleteCardType = async (id) => {
    try {
      const response = await fetch(`${backendUrl}/card-types/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: 'Card type deleted successfully',
          life: 3000
        });
        setLocalData(localData.filter(item => item.id !== id));
      } else {
        const errorData = await response.json();
        toast.current.show({
          severity: 'error',
          summary: 'Error',
          detail: errorData.message || 'Failed to delete card type',
          life: 3000
        });
      }
    } catch (error) {
      console.error('Error:', error);
      toast.current.show({
        severity: 'error',
        summary: 'Error',
        detail: 'An error occurred while deleting card type',
        life: 3000
      });
    }
  };



  return (
    <Container>
      <Toast ref={toast} />
      <ConfirmDialog />

      {/* Header Section */}
      <div className="w-full flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
        <div className="mb-4 lg:mb-0">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Card Types</h1>
          <p className="text-gray-600">Manage and preview your card type designs</p>
        </div>

        <div className="flex flex-col sm:flex-row gap-3 items-stretch sm:items-center">
          {/* Search Bar */}
          <div className="relative">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
            <input
              type="text"
              placeholder="Search card types..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all duration-200 w-full sm:w-64"
            />
          </div>

          {/* Create Button */}
          <motion.button
            className="main-btn text-md shadow-md px-6 py-2 flex items-center justify-center gap-2"
            onClick={openCreateTypeModal}
          >
            <span className="text-lg">+</span>
            Create New Type
          </motion.button>
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading card types...</p>
          </div>
        </div>
      )}

      {/* Carousel Section */}
      {!isLoading && filteredCards.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <CardCarousel
            cards={filteredCards}
            onCardSelect={handleCardSelect}
            selectedCard={selectedCard}
            itemsPerView={{
              desktop: 3,
              tablet: 2,
              mobile: 1
            }}
          />
        </motion.div>
      )}

      {/* Empty State */}
      {!isLoading && filteredCards.length === 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="flex flex-col items-center justify-center h-64 text-gray-500"
        >
          <div className="text-center">
            <div className="text-6xl mb-4">🔍</div>
            {searchTerm ? (
              <>
                <h3 className="text-xl font-semibold text-gray-700 mb-2">
                  No card types found
                </h3>
                <p className="text-gray-600 mb-4">
                  No card types match your search for "{searchTerm}"
                </p>
                <motion.button
                  onClick={() => setSearchTerm('')}
                  className="main-btn px-4 py-2"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Clear Search
                </motion.button>
              </>
            ) : (
              <>
                <h3 className="text-xl font-semibold text-gray-700 mb-2">
                  No card types available
                </h3>
                <p className="text-gray-600 mb-4">
                  Get started by creating your first card type
                </p>
                <motion.button
                  onClick={openCreateTypeModal}
                  className="main-btn px-6 py-3 flex items-center gap-2"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <span className="text-lg">+</span>
                  Create First Card Type
                </motion.button>
              </>
            )}
          </div>
        </motion.div>
      )}

      {/* Action Panel for Selected Card */}
      <AnimatePresence>
        {selectedCard && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="mt-8 p-6 bg-gradient-to-r from-teal-50 to-cyan-50 rounded-xl border border-teal-200 shadow-lg"
          >
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
              <div className="mb-4 lg:mb-0">
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {selectedCard.name}
                </h3>
                <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-600">Type:</span>
                    <p className="text-gray-900">{selectedCard.type_of_connection}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Colors:</span>
                    <p className="text-gray-900">{selectedCard.number_of_colors}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Width:</span>
                    <p className="text-gray-900">{selectedCard.setting?.width}px</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Height:</span>
                    <p className="text-gray-900">{selectedCard.setting?.height}px</p>
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-3">
                <motion.button
                  onClick={() => handleCardEdit(selectedCard)}
                  className="main-btn px-6 py-3 flex items-center justify-center gap-2 font-medium shadow-md"
                  //whileHover={{ scale: 1.02, y: -1 }}
                  //whileTap={{ scale: 0.98 }}
                >
                  <FiChevronRight size={18} />
                  Edit Type
                </motion.button>

                <motion.button
                  onClick={() => handleCardDelete(selectedCard)}
                  className="px-6 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors duration-200 flex items-center justify-center gap-2 font-medium shadow-md"
                  //whileHover={{ scale: 1.02, y: -1 }}
                  //whileTap={{ scale: 0.98 }}
                >
                  <FiTrash size={18} />
                  Delete Type
                </motion.button>

                <motion.button
                  onClick={() => setSelectedCard(null)}
                  className="gray-btn px-6 py-3 flex items-center justify-center gap-2 font-medium shadow-md"
                  whileHover={{ scale: 1.02, y: -1 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Clear Selection
                </motion.button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Create/Edit Modal */}
      <CreateTypeForm
        isModalOpen={isCreateTypeModalOpen}
        setIsModalOpen={setIsCreateTypeModalOpen}
        fetchCardTypes={refetch}
        editData={editData}
        isEditMode={isEditMode}
        resetEditMode={resetEditMode}
      />
    </Container>
  );
}

export default CardsIndex;